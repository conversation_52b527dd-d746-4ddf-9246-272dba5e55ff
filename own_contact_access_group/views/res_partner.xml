<odoo>
    <data>
        <!-- inhert & edit account journal form view  -->
        <!-- <record id="ro_res_partner_access_inherit" model="ir.ui.view">
            <field name="name">res.partner.access.inheritd</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page name="user_access" string="User Access" groups="own_contact_access_group.access_user_allowed_group" attrs="{'invisible': [('company_type','!=','person')]}">
                        <group>
                            <group>
                                <field name="allowed_user_ids" />
                            </group>
                            <group>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record> -->
    </data>
</odoo>
