<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    
<!-- 
    <record id="access_user_allowed_group" model="res.groups">
        <field name="name">Contact Allowed User Manager[Atlasco]</field>
    </record> -->

    <record id="access_user_own_allowed_group" model="res.groups">
        <field name="name">User Own Allowed Contact [Alasdekaa]</field>
    </record>
    <record id="access_user_all_allowed_group" model="res.groups">
        <field name="name">User All Contacts [Alasdekaa]</field>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>


    <!-- Res Partner Access -->
    <!-- remove internal user and add spcific groups -->
    <record id="base.res_partner_rule_private_employee" model="ir.rule">
        <field name="name">res.partner.rule.private.employee</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="domain_force">
            ['|', ('type', '!=', 'private'), ('type', '=', False)]
        </field>
        <field name="groups" eval="[
            (4, ref('own_contact_access_group.access_user_all_allowed_group')),
        ]"/>
        <!-- (5,0,0) -->
        <!-- (4, ref('mrp.group_mrp_user')), -->
        <!-- (4, ref('account.group_account_invoice')), -->
        <!-- (4, ref('hr.group_hr_user')), -->
        <!-- (4, ref('stock.group_stock_user')), -->
        <!-- (4, ref('sales_team.group_sale_salesman_all_leads')), -->
        <!-- (4, ref('purchase.group_purchase_manager')), -->

    </record>

    <record id="own_docs_view_own_res_partner_rule" model="ir.rule">
        <field name="name">Only see Own Partners</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="domain_force">['|',('user_id','=',user.id),('user_id','=',False)]</field>
        <!-- <field name="domain_force">['|',('allowed_user_ids','=',user.id),('allowed_user_ids','=',False)]</field> -->
        <field name="groups" eval="[
            (4, ref('own_contact_access_group.access_user_own_allowed_group')),
        ]"/>
        <!-- (4, ref('purchase.group_purchase_user')),
        (4, ref('sales_team.group_sale_salesman')) -->
    </record>



</odoo>