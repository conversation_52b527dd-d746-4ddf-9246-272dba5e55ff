# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class AccountMove(models.Model):
    _inherit = 'account.move'


    @api.onchange('partner_id')
    def update_journal(self):
        for invoice in self:
            if invoice.is_sale_document(include_receipts=True):
                invoice.journal_id = self.partner_id.invoice_journal_id or invoice.journal_id

    def _search_default_journal(self):
        res = super(AccountMove, self)._search_default_journal()
        journal = res
        if self.is_sale_document(include_receipts=True):
            res = self.partner_id.invoice_journal_id or journal

        return res