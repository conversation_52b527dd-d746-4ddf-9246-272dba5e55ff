# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def action_confirm(self):
        res = super(SaleOrder, self).action_confirm()
        
        for this in self:
            if len(this.sudo().partner_invoice_id.child_ids)!=0:
                raise UserError("You can't confirm with Invoice Address his Sub Partners")
        return res
