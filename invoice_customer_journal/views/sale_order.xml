<odoo>
  <data>

    <record id="view_sale_order_form_invoice_partner" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
          <xpath expr="//field[@name='partner_id']" position="attributes"> 
            <attribute name="domain">[('customer_rank','&gt;',0), ('company_id','in',(False,company_id))]</attribute>
          </xpath> 

            <xpath expr="//field[@name='partner_invoice_id']" position="attributes"> 
              <attribute name="domain">['|',('parent_id','=',partner_id), ('id','=',partner_id)]</attribute>
            </xpath> 

            <xpath expr="//field[@name='partner_shipping_id']" position="attributes"> 
              <attribute name="domain">['|',('parent_id','=',partner_id), ('id','=',partner_id)]</attribute>
            </xpath> 

        </field>
    </record>

  </data>
</odoo>
