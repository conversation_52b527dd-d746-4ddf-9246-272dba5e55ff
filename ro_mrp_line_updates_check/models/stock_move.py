from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class StockMove(models.Model):
    _inherit = "stock.move"

    original_uom_qty = fields.Float(
        "Original Quantity", compute="_compute_original_qty", store=False, readonly=True
    )

    @api.depends(
        "product_id", "raw_material_production_id", "raw_material_production_id.bom_id"
    )
    def _compute_original_qty(self):
        for move in self:
            move.original_uom_qty = move.product_uom_qty
            if (
                move.raw_material_production_id
                and move.raw_material_production_id.bom_id
            ):
                bom_line = move.env["mrp.bom.line"].search(
                    [
                        ("bom_id", "=", move.raw_material_production_id.bom_id.id),
                        ("product_id", "=", move.product_id.id),
                    ],
                    limit=1,
                )
                if bom_line:
                    move.original_uom_qty = bom_line.product_qty * ((move.raw_material_production_id.product_qty/move.raw_material_production_id.bom_id.product_qty) if move.raw_material_production_id.bom_id.product_qty else 0)
                    
                    move.bom_line_id = bom_line.id
                else:
                    move.original_uom_qty = move.product_uom_qty

    created_by_user = fields.Boolean("Created by User", default=False)

    @api.model
    def create(self, vals):
        # Check if this move was created manually by the user
        if not self.env.context.get("default_bom_line_id") and not vals.get(
            "bom_line_id"
        ):
            vals["created_by_user"] = True
        else:
            vals["created_by_user"] = False

        return super(StockMove, self).create(vals)

    def write(self, vals):
        if "product_uom_qty" in vals:
            for move in self:
                move._compute_original_qty()

        return super(StockMove, self).write(vals)

    def unlink(self):
        for move in self:
            message = _("The product ----- %s ----- was deleted by %s.") % (
                move.product_id.display_name,
                self.env.user.name,
            )
            if move.raw_material_production_id:
                move.raw_material_production_id.message_post(body=message)

        return super(StockMove, self).unlink()
