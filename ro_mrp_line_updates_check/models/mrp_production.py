from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_is_zero


class MrpProduction(models.Model):
    _inherit = "mrp.production"

    changes_confirmed = fields.Boolean(string="Changes Approved", tracking=True)
    changes = fields.Boolean(
        string="Quantity Changed",
        compute="_check_move_changes",
        store=True,
    )

    # original_product_ids = fields.Many2many(
    #     "product.product",
    #     compute="_compute_original_product_ids",
    #     string="Original Products",
    #     store=True,
    # )

    deleted_moves = fields.Boolean(
        string="Products Changed",
        compute="_check_deleted_moves",
        store=True,
    )

    is_manager = fields.Boolean(
        compute="_check_user_group",
        default=lambda self: self.env.user.has_group(
            "ro_mrp_line_updates_check.manifacturing_order_manager"
        ),
    )
    @api.depends("move_raw_ids","product_id")
    def _check_user_group(self):
        self.is_manager = self.env.user.has_group(
            "ro_mrp_line_updates_check.manifacturing_order_manager"
        )

    def action_confirm(self):
        for order in self:
            if (order.changes or order.deleted_moves) and not order.changes_confirmed:
                raise UserError(
                    _(
                        "You cannot confirm this manufacturing order because changes were made to the lines. Please review and confirm the changes before proceeding."
                    )
                )
        res = super(MrpProduction, self).action_confirm()
        for order in self:
            order.qty_producing = order.product_qty
            order._onchange_producing()
        return res
    
    def button_mark_done(self):
        for order in self:
            if (order.changes or order.deleted_moves) and not order.changes_confirmed:
                raise UserError(
                    _(
                        "You cannot confirm this manufacturing order because changes were made to the lines. Please review and confirm the changes before proceeding."
                    )
                )
        return super(MrpProduction, self).button_mark_done()

    @api.depends("move_raw_ids", "move_raw_ids.quantity_done")
    def _check_move_changes(self):
        for production in self:
            production.changes = False
            for move in production.move_raw_ids:
                if sum(production.move_raw_ids.mapped('quantity_done')) > 0:
                    qty = move.quantity_done
                else:
                    qty = move.product_uom_qty if not move.quantity_done else move.quantity_done

                #replace computation
                original_uom_qty = 0
                if move.bom_line_id:
                   original_uom_qty = move.bom_line_id.product_qty * (move.raw_material_production_id.product_qty/move.raw_material_production_id.bom_id.product_qty)
                else:
                    bom_line = move.env["mrp.bom.line"].search(
                        [
                            ("bom_id", "=", move.raw_material_production_id.bom_id.id),
                            ("product_id", "=", move.product_id.id),
                        ],
                        limit=1,
                    )
                    if bom_line:
                        original_uom_qty = bom_line.product_qty * (move.raw_material_production_id.product_qty/move.raw_material_production_id.bom_id.product_qty)
                        move.bom_line_id = bom_line
                    else:
                        original_uom_qty = move.product_uom_qty
                #replace computation

                if qty and original_uom_qty == 0 and not move.bom_line_id:
                    production.deleted_moves = True
                elif not float_is_zero(
                    qty - original_uom_qty,
                    precision_rounding=.001,
                    # precision_rounding=move.product_uom.rounding,
                ):
                    production.changes = True
                    break

    # @api.depends("move_raw_ids")
    # def _compute_original_product_ids(self):
    #     """Store the product_ids of the original move lines when the production order is created,
    #     excluding products with original_uom_qty = 0."""
    #     for production in self:
    #         if not production.original_product_ids:
    #             # Filter out moves with original_uom_qty = 0
    #             valid_moves = production.move_raw_ids.filtered(
    #                 lambda move: move.bom_line_id
    #                 # lambda move: move.original_uom_qty != 0
    #             )
    #             production.original_product_ids = [
    #                 (6, 0, valid_moves.mapped("product_id").ids)
    #             ]

    @api.depends("move_raw_ids")
    def _check_deleted_moves(self):
        """Check if any of the original product lines were deleted."""
        for production in self:
            current_product_ids = set(production.move_raw_ids.mapped("product_id").ids)
            original_product_ids = set(production.bom_id.bom_line_ids.mapped("product_id").ids)
            if original_product_ids != current_product_ids:
                production.deleted_moves = True

            else:
                production.deleted_moves = False

    def write(self, vals):
        original_moves = set(self.move_raw_ids.mapped("product_id.id"))
        result = super(MrpProduction, self).write(vals)
        new_moves = set(self.move_raw_ids.mapped("product_id.id"))
        added_moves = new_moves - original_moves
        if added_moves:
            added_products = self.env["product.product"].browse(added_moves)
            for product in added_products:
                message = _("The product ----- %s ----- was added by %s.") % (
                    product.display_name,
                    self.env.user.name,
                )
                self.message_post(body=message)

        return result
    # , self._remove_duplicate_user_created_lines()

    # def _remove_duplicate_user_created_lines(self):

    #     for production in self:
    #         product_move_map = {}

    #         for move in production.move_raw_ids:
    #             if move.product_id in product_move_map:
    #                 if move.created_by_user:
    #                     move.unlink()
    #                 else:
    #                     product_move_map[move.product_id].unlink()
    #                     product_move_map[move.product_id] = move
    #             else:
    #                 product_move_map[move.product_id] = move

    #     return True
