from odoo import models, api, fields, exceptions

class AccountPayment(models.Model):
    _inherit = 'account.payment'

    def  action_post(self):
        for payment in self:
            if payment.payment_type == 'outbound' and not payment.journal_id.payment_issued_bool:

                account = payment.payment_method_line_id.payment_account_id
                
                if account.current_balance - payment.amount < 0:
                    raise exceptions.ValidationError("Negative balance is not allowed for cash and bank accounts.")

        return super(AccountPayment, self). action_post()
