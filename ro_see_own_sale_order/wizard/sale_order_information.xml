<odoo>
    <data>
        
        <record id="ro_sale_order_information_form_view" model="ir.ui.view">
            <field name="name">sale_order_information view</field>
            <field name="model">sale.order.information</field>
            <field name="arch" type="xml">
                <form string="Sale Order">
                    <group>
                        <group>
                            <field name="sale_order"/>
                        </group>
                    </group>
                    <footer>
                        <button name="search_sale_order" string="Find" type="object" class="oe_highlight"/>
                        <button string="Cancel" class="oe_highlight" special="cancel" />
                    </footer>
                </form>
            </field>
        </record>
        <record model="ir.actions.act_window" id="ro_action_sale_order_info">
            <field name="name">Sale Order Info</field>
            <field name="res_model">sale.order.information</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ro_sale_order_information_form_view"/>
            <field name="target">new</field>
        </record>

        <menuitem id="ro_menu_sale_order_information" name="Sale Order Info"
         groups="sales_team.group_sale_salesman"
         action="ro_action_sale_order_info"
         parent="sale.sale_menu_root" sequence="10"/>
    </data>
</odoo>