# -*- coding: utf-8 -*-

from datetime import datetime

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SaleOrderInformation(models.TransientModel):
    _name = 'sale.order.information'
    _description = 'Sale Order Information'

    sale_order = fields.Char('Sale Order')
    

    def search_sale_order(self):
        sale_order = False
        
        if self.sale_order:
            sale_order = self.env['sale.order'].search([('name','=',self.sale_order)])
        
        if not sale_order:
            raise UserError(_('No Sale Order Found'))

        if len(sale_order) == 1:
            view_id = self.env.ref('sale.view_order_form').id

            return {
                'name': _('Sale Order'),
                'view_mode': 'form',
                'res_model': 'sale.order',
                'res_id': sale_order.id,
                'view_id': view_id,
                'type': 'ir.actions.act_window'
            }
        else:

            
            action = self.env["ir.actions.actions"]._for_xml_id("sale.action_quotations_with_onboarding")

            action['domain'] = [('id','in',sale_order.ids)]

            return action                                                                                                                                                                                                                 
        