# # -*- coding: utf-8 -*-

# from odoo import models, fields, api, _


# class ResPartner(models.Model):
    
#     _inherit = 'res.partner'

#     sale_order_count_own = fields.Integer(compute='_compute_sale_order_count_own', string='Sale Order Count.')

#     def _compute_sale_order_count_own(self):
#         if self.sale_order_ids:
#             sale_order_ids = self.sale_order_ids.filtered(lambda x:x.invoice_status != 'invoiced')
#             self.sale_order_count_own = len(sale_order_ids)
#         else:
#             self.sale_order_count_own = 0


        