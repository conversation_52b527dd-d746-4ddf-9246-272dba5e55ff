<odoo>
  <data>
      <record id="action_quotations_with_onboarding_own" model="ir.actions.act_window">
          <field name="name">Quotations OW</field>
          <field name="type">ir.actions.act_window</field>
          <field name="res_model">sale.order</field>
          <field name="view_id" ref="sale.view_quotation_tree_with_onboarding"/>
          <field name="view_mode">tree,kanban,form,calendar,pivot,graph,activity</field>
          <field name="search_view_id" ref="sale.sale_order_view_search_inherit_quotation"/>
          <field name="context">{'search_default_my_quotation': 1}</field>
          <field name="domain">[('invoice_status', '!=', 'invoiced'), ('state', '!=', 'cancel')]</field>
          <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                  Create a new quotation, the first step of a new sale!
              </p>
              <p>
                  Once the quotation is confirmed by the customer, it becomes a sales order.
                  <br/> You will be able to create an invoice and collect the payment.
              </p>
          </field>
      </record>
      <menuitem id="menu_sale_quotations_own"
              action="action_quotations_with_onboarding_own"
              parent="sale.sale_order_menu"
              sequence="1" groups="sales_team.group_sale_salesman"/>
      <menuitem id="sale.menu_sale_quotations"
              action="sale.action_quotations_with_onboarding"
              parent="sale.sale_order_menu"
              sequence="1" groups="sales_team.group_sale_salesman_all_leads"/>
      <menuitem id="sale.menu_sale_order"
          name="Orders"
          action="sale.action_orders"
          parent="sale.sale_order_menu"
          sequence="2" groups="sales_team.group_sale_salesman_all_leads"/>
      <menuitem id="sale.menu_sale_invoicing"
          name="To Invoice"
          parent="sale.sale_menu_root"
          sequence="3" groups="sales_team.group_sale_salesman_all_leads"/>

  </data>
</odoo>