<odoo>
    <record model="ir.ui.view" id="stock_picking_form_validate">
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <!-- <xpath expr="//form" position="attributes">
                <attribute name="create">0</attribute>
            </xpath> -->
            <xpath expr="//form" position="attributes">
                <attribute name="delete">0</attribute>
            </xpath>
            <!-- <xpath expr="//div[@name='button_box']" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath> -->
            <xpath expr="//button[@name='button_validate'][1]" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath>
            <xpath expr="//button[@name='button_validate'][2]" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath>
            <xpath expr="//button[@name='action_assign']" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath>
            <xpath expr="//button[@name='%(stock.act_stock_return_picking)d']" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath>
            <xpath expr="//button[@name='do_print_picking']" position="replace">
            </xpath>
            <xpath expr="//button[@name='%(stock.action_report_delivery)d']" position="replace">
            </xpath>
            <xpath expr="//button[@name='do_unreserve']" position="attributes">
                <attribute name="groups">base.group_system</attribute>
            </xpath>
            <xpath expr="//button[@name='button_scrap']" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath>
            <xpath expr="//button[@name='action_toggle_is_locked'][1]" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath>
            <xpath expr="//button[@name='action_toggle_is_locked'][2]" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
            </xpath>
            <xpath expr="//button[@name='action_cancel']" position="attributes">
                <attribute name="groups">whole_transfer.group_whole_transfer_responsible</attribute>
                <attribute name="attrs">{'invisible': ['|',('picking_type_code', '!=', 'internal'),('state', 'not in', ('assigned', 'confirmed', 'draft', 'waiting'))]}</attribute>
            </xpath>

            <xpath expr="//button[@name='action_cancel']" position="after">
                <button name="action_cancel" string="Cancel" groups="base.group_system" type="object" data-hotkey="z" attrs="{'invisible': [('state', 'not in', ('assigned', 'confirmed', 'draft', 'waiting','validate2'))]}"/>
            </xpath>
        </field>
    </record>
    <record model="ir.ui.view" id="stock_picking_vpicktree_view">
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.vpicktree"/>
        <field name="arch" type="xml">
            <!-- <xpath expr="//tree" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//tree" position="attributes">
                <attribute name="multi_edit">0</attribute>
            </xpath>
            <xpath expr="//tree" position="attributes">
                <attribute name="delete">0</attribute>
            </xpath> -->
            <xpath expr="//tree" position="attributes">
                <attribute name="default_order">create_date desc</attribute>
            </xpath>
            <xpath expr="//button[@name='do_unreserve']" position="attributes">
                <attribute name="groups">base.group_system</attribute>
            </xpath>
        </field>
    </record>
    <!-- <record model="ir.ui.view" id="stock_picking_vpicktree_view_manager">
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.vpicktree"/>
        <field name="groups_id" eval="[(4, ref('whole_sale.group_whole_sale_manager'))]"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="create">1</attribute>
            </xpath>
        </field>
    </record> -->
    <record model="ir.ui.view" id="ro_stock_picking_stock_picking_kanban">
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.stock_picking_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//kanban" position="attributes">
                <attribute name="delete">0</attribute>
            </xpath>
            <xpath expr="//kanban" position="attributes">
                <attribute name="default_order">create_date desc</attribute>
            </xpath>
        </field>
    </record>
    
    <record id="stock.action_unreserve_picking" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('base.group_system'))]"/>
    </record>
    <record id="view_backorder_confirmation_no_backorder" model="ir.ui.view">
        <field name="model">stock.backorder.confirmation</field>
        <field name="inherit_id" ref="stock.view_backorder_confirmation"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='pick_ids']" position="after">
                <field name="picking_type_code" invisible='1'/>
            </xpath>
            <xpath expr="//button[@name='process_cancel_backorder']" position="attributes">
                <attribute name="attrs">{'invisible': ['|',('picking_type_code','=','outgoing'),('show_transfers', '=', True)]}</attribute>
            </xpath>
        </field>
    </record>
</odoo>