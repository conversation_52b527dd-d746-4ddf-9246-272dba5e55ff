<odoo>
    <data>
        <record id="view_whole_transfers_filter" model="ir.ui.view">
            <field name="name">whole.transfer.list.select</field>
            <field name="model">whole.transfer</field>
            <field name="priority" eval="15"/>
            <field name="arch" type="xml">
                <search string="Search Picking Lists">
                    <field name="name" string="Transfer" filter_domain="[('name', 'ilike', self)]"/>
                    <field name="user_id"/>
                    <field name="team_id" string="Sales Team"/>
                    <field name="order_line" string="Product" filter_domain="[('order_line.product_id', 'ilike', self)]"/>
                    <!-- We only allow to search on the following sale order line fields (product, name) because the other fields, such as price, quantity, ...
                        will not be searched as often, and if they need to be searched it's usually in the context of products
                        and then they can be searched from the page listing the sale order lines related to a product (from the product itself).
                    -->
                    <filter string="My Orders" domain="[('user_id', '=', uid)]" name="my_sale_orders_filter"/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue" domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]" help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today" domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all" domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Salesperson" name="salesperson" domain="[]" context="{'group_by': 'user_id'}"/>
                        <filter string="Order Date" name="order_month" domain="[]" context="{'group_by': 'date_order'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="view_whole_transfer_tree" model="ir.ui.view">
            <field name="name">whole.transfer.tree</field>
            <field name="model">whole.transfer</field>
            <field name="priority">4</field>
            <field name="arch" type="xml">
                <tree string="Whole Transfer" js_class="lazy_column_list" multi_edit="1" sample="1" decoration-info="state in ['draft']" decoration-muted="state == 'cancel'">
                    <field name="name" string="Number" readonly="1" decoration-bf="1"/>
                    <field name="date_order" string="Order Date" optional="show"/>
                    <field name="user_id" widget="many2one_avatar_user" optional="show"/>
                    <field name="activity_ids" widget="list_activity" optional="show"/>
                    <field name="src_group_location_id" />
                    <field name="team_id" optional="hide"/>
                    <field name="state" decoration-success="state == 'done'" decoration-info="state == 'draft'" widget="badge" optional="show"/>
                    <field name="message_needaction" invisible="1"/>
                </tree>
            </field>
        </record>
        <!-- Whole Transfer Kanban View  -->
        <record model="ir.ui.view" id="view_whole_transfer_order_kanban">
            <field name="name">whole.transfer.kanban</field>
            <field name="model">whole.transfer</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <field name="name"/>
                    <field name="date_order"/>
                    <field name="state"/>
                    <field name="activity_state"/>
                    <progressbar field="activity_state" colors="{&quot;planned&quot;: &quot;success&quot;, &quot;today&quot;: &quot;warning&quot;, &quot;overdue&quot;: &quot;danger&quot;}"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top mb16">
                                    <div class="o_kanban_record_headings mt4">
                                        <strong class="o_kanban_record_title">
                                            <span>
                                                <t t-esc="record.name"/>
                                            </span>
                                        </strong>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left text-muted">
                                        <span>
                                            <t t-esc="record.name.value"/>
                                            <t t-esc="record.date_order.value"/>
                                        </span>
                                        <field name="activity_ids" widget="kanban_activity"/>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'cancel': 'default', 'done': 'success'}}"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <record id="view_whole_transfer_form" model="ir.ui.view">
            <field name="name">whole.transfer.form</field>
            <field name="model">whole.transfer</field>
            <field name="arch" type="xml">
                <form string="Whole Transfers">
                    <header>
                        <button name="action_confirm" data-hotkey="v" string="Confirm" type="object" attrs="{'invisible': [('state', 'not in', ['draft'])]}" groups="whole_transfer.group_whole_transfer_responsible"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,configured,sale"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <!-- Transfer  button -->
                            <button name="button_open_transfers" type="object" class="oe_stat_button" icon="fa-bars">
                            Transfers
                        </button>
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <group name="whole_header">
                            <group name="partner_details">
                                <!-- <field name="warehouse_id" attrs="{'readonly':[('state','!=','draft')]}"/> -->
                                <field name="src_group_location_id" options="{'no_create':True, 'no_create_edit': True, 'no_open': True}" attrs="{'readonly':[('state','!=','draft')]}"/>
                                <field name="dest_group_location_id" options="{'no_create':True, 'no_create_edit': True, 'no_open': True}" attrs="{'readonly':[('state','!=','draft')]}"/>
                                <!-- <field name="picking_type_id" attrs="{'readonly':[('state','!=','draft')]}"/> -->
                            </group>
                            <group name="order_details">
                                <div class="o_td_label" attrs="{'invisible': [('state', 'in', ['draft', 'sent'])]}">
                                    <label for="date_order" string="Order Date"/>
                                </div>
                                <field name="date_order" attrs="{'required': [('state', 'in', ['sale', 'done'])], 'invisible': [('state', 'in', ['draft', 'sent'])]}" nolabel="1"/>
                                <field name="is_transfer_in" invisible="1"/>
                                <field name="is_transfer_out" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Order Lines" name="order_lines">
                                <!-- widget="section_and_note_one2many" -->
                                <field name="order_line" mode="tree,kanban" attrs="{'readonly': [('state', 'in', ('done','cancel'))]}">
                                    <form>
                                        <!--
                                      We need the sequence field to be here for new lines to be added at the correct position.
                                      TODO: at some point we want to fix this in the framework so that an invisible field is not required.
                                  -->
                                        <field name="sequence" invisible="1"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <group>
                                            <group>
                                                <field name="product_id" domain="[('sale_ok', '=', True)]" context="{'quantity':product_uom_qty, 'uom':product_uom}" required="1" force_save="1" widget="many2one_barcode"/>
                                                <label for="product_uom_qty"/>
                                                <div class="o_row" name="ordered_qty">
                                                    <field context="{'quantity':product_uom_qty, 'uom':product_uom, 'uom_qty_change':True}" name="product_uom_qty"/>
                                                    <field name="product_uom" force_save="1" groups="uom.group_uom" class="oe_no_button" required="1"/>
                                                    <field name="product_uom" groups="!uom.group_uom" invisible="1"/>
                                                </div>
                                                <!--
                                              We need the sequence field to be here
                                              because we want to be able to overwrite the default sequence value in the JS
                                              in order for new lines to be added at the correct position.
                                              NOTE: at some point we want to fix this in the framework so that an invisible field is not required.
                                          -->
                                                <field name="sequence" invisible="1"/>
                                            </group>
                                        </group>
                                        <!-- <label for="name" string="Description"/>
                                  <field name="name"/> -->
                                        <field name="state" invisible="1"/>
                                    </form>
                                    <tree string="Whole Transfers Lines" editable="bottom">
                                        <!-- <control>
                                      <create name="add_product_control" string="Add a product"/>
                                  </control> -->
                                        <field name="sequence" widget="handle"/>
                                        <!-- We do not display the type because we don't want the user to be bothered with that information if he has no section or note. -->
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="product_id" required="1" force_save="1" context="{                                           'quantity': product_uom_qty,                                           'uom':product_uom,                                       }" domain="[('sale_ok', '=', True)]" widget="product_configurator"/>
                                        <!-- 'group_from':parent.src_group_location_id,
                                  'group_to':parent.dest_group_location_id, -->
                                        <!-- <field name="name" optional="hide"/> -->
                                        <field name="product_uom_qty" context="{'quantity': product_uom_qty,'uom': product_uom}"/>
                                        <field name="product_uom" force_save="1" string="UoM" required="1" options="{&quot;no_open&quot;: True}" optional="show"/>
                                        <field name="product_uom" groups="!uom.group_uom" invisible="1"/>
                                        <field name="src_qty"/>
                                        <field name="dest_qty"/>
                                        <field name="state" invisible="1"/>
                                    </tree>
                                    <kanban class="o_kanban_mobile">
                                        <!-- <field name="name"/> -->
                                        <field name="product_id"/>
                                        <field name="product_uom_qty"/>
                                        <field name="product_uom" groups="!uom.group_uom" invisible="1"/>
                                        <field name="product_uom" groups="uom.group_uom"/>
                                        <templates>
                                            <t t-name="kanban-box">
                                                <div t-attf-class="oe_kanban_card oe_kanban_global_click pl-0 pr-0 ">
                                                    <div class="row no-gutters">
                                                        <div class="col-2 pr-3">
                                                            <img t-att-src="kanban_image('product.product', 'image_128', record.product_id.raw_value)" t-att-title="record.product_id.value" t-att-alt="record.product_id.value" style="max-width: 100%;"/>
                                                        </div>
                                                        <div class="col-10">
                                                            <div class="row">
                                                                <div class="col">
                                                                    <strong t-esc="record.product_id.value"/>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-12 text-muted">
                                                                Quantity:
                                                                    <t t-esc="record.product_uom_qty.value"/>
                                                                    <t t-esc="record.product_uom.value"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </t>
                                        </templates>
                                    </kanban>
                                </field>
                                <group name="note_group" col="6" class="mt-2 mt-md-0">
                                    <group colspan="4">
                                        <field name="note" nolabel="1" placeholder="Terms and conditions..."/>
                                    </group>
                                    <div class="oe_clear"/>
                                </group>
                            </page>
                            <page string="Other Info" name="other_information" groups="whole_transfer.group_whole_transfer_responsible">
                                <group>
                                    <group>
                                        <field name="user_id" domain="[('share', '=', False)]" widget="many2one_avatar_user"/>
                                        <field name="team_id" kanban_view_ref="%(sales_team.crm_team_view_kanban)s" options="{'no_create': True}"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        <record id="view_whole_transfer_out_form" model="ir.ui.view">
            <field name="name">whole.transfer.out.form</field>
            <field name="model">whole.transfer</field>
            <field name="inherit_id" ref="view_whole_transfer_form"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <!-- <xpath expr="//field[@name='picking_type_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath> -->
                <xpath expr="//field[@name='src_group_location_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_whole_transfer_in_form" model="ir.ui.view">
            <field name="name">whole.transfer.in.form</field>
            <field name="model">whole.transfer</field>
            <field name="inherit_id" ref="view_whole_transfer_form"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <!-- <xpath expr="//field[@name='picking_type_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath> -->
                <xpath expr="//field[@name='dest_group_location_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
            </field>
        </record>
        <record id="action_whole_transfer_orders_in" model="ir.actions.act_window">
            <field name="name">Whole Transfers In</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">whole.transfer</field>
            <field name="view_mode">tree,kanban,form,pivot,graph,activity</field>
            <field name="search_view_id" ref="view_whole_transfers_filter"/>
            <field name="context">{'default_is_transfer_in': True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                  Create a new Order, the first step of a new Transfer!
                </p>
                <p>
                    Once the quotation is confirmed, it becomes a internal transfer.
                    <br/>
                </p>
            </field>
            <field name="domain">[('is_transfer_in','=',True),('team_id.member_ids','=', uid)]</field>
        </record>
        <record id="action_whole_transfer_orders_out" model="ir.actions.act_window">
            <field name="name">Whole Transfers Out</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">whole.transfer</field>
            <field name="view_mode">tree,kanban,form,pivot,graph,activity</field>
            <field name="search_view_id" ref="view_whole_transfers_filter"/>
            <field name="context">{'default_is_transfer_out': True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                  Create a new Order, the first step of a new Transfer!
                </p>
                <p>
                    Once the quotation is confirmed, it becomes a internal transfer.
                    <br/>
                </p>
            </field>
            <field name="domain">[('is_transfer_out','=',True),('team_id.member_ids','=', uid)]</field>
        </record>
        <record id="whole_transfer_action_view_order_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="whole_transfer.view_whole_transfer_tree"/>
            <field name="act_window_id" ref="action_whole_transfer_orders_in"/>
        </record>
        <record id="whole_transfer_action_view_order_out_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="whole_transfer.view_whole_transfer_tree"/>
            <field name="act_window_id" ref="action_whole_transfer_orders_out"/>
        </record>
        <record id="whole_transfer_action_view_order_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="whole_transfer.view_whole_transfer_order_kanban"/>
            <field name="act_window_id" ref="action_whole_transfer_orders_in"/>
        </record>
        <record id="whole_transfer_action_view_order_out_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="whole_transfer.view_whole_transfer_order_kanban"/>
            <field name="act_window_id" ref="action_whole_transfer_orders_out"/>
        </record>
        <record id="whole_transfer_action_view_order_form" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="whole_transfer.view_whole_transfer_in_form"/>
            <field name="act_window_id" ref="action_whole_transfer_orders_in"/>
        </record>
        <record id="whole_transfer_action_view_order_out_form" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="whole_transfer.view_whole_transfer_out_form"/>
            <field name="act_window_id" ref="action_whole_transfer_orders_out"/>
        </record>
        <!-- <menuitem id="menu_whole_transfer_order"
            name="Transfers"
            parent="whole_sale.whole_sale_menu_root"
            sequence="10" /> -->
        <menuitem id="whole_transfer_menu_root"
            name="Whole Transfer"
            web_icon="whole_transfer,static/description/icon.png"
            sequence="31"
            groups="group_whole_transfer_user"/>
        <!-- <menuitem id="menu_whole_internal_transfer_order"
            name="Internal Transfers"
            parent="whole_transfer_menu_root"
            groups="group_whole_transfer_user"
            sequence="1" />
        <menuitem id="menu_whole_transfer_order_in"
            name="Request in"
            action="action_whole_transfer_orders_in"
            parent="whole_transfer.menu_whole_internal_transfer_order"
            groups="group_whole_transfer_user"
            sequence="5" />
        <menuitem id="menu_whole_transfer_order_out"
            name="Request out"
            action="action_whole_transfer_orders_out"
            parent="whole_transfer.menu_whole_internal_transfer_order"
            groups="group_whole_transfer_user"
            sequence="6" /> -->
        <!--Hide inventory menu-->
        <record id="stock.menu_stock_root" model="ir.ui.menu">
            <field name="groups_id" eval="[(6,0,[ref('stock.group_stock_manager')])]" />
        </record>
        <record id="action_transfer_internal_picking" model="ir.actions.act_window">
            <field name="name">Internal Transfer</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('stock.vpicktree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stock.view_picking_form')})]"/>
            <field name="context">{'default_company_id': allowed_company_ids[0], 'contact_display': 'partner_address'}
            </field>
            <field name="domain">[('picking_type_code','=','internal'),('state','not in',('done','cancel')),'|',('location_id.location_user_ids','=', uid),('location_dest_id.location_user_ids','=', uid)]</field>
        </record>
        <!-- <menuitem id="menu_picking_transfer_order_internal"
            name="Internal Transfer"
            action="action_transfer_internal_picking"
            parent="whole_transfer.menu_whole_internal_transfer_order"
            sequence="6" /> -->
    </data>
</odoo>
