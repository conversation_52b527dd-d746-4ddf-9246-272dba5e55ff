from odoo import models, fields, api
from num2words import num2words


class HrContract(models.Model):
    _inherit = 'hr.contract'

    ro_numinwords = fields.Char(string='Total in words',compute='_getnumbernew',store=False)


    @api.depends('ro_net_income')
    def _getnumbernew(self):
        for rec in self:
            # ro_numinwords = num2words(float(rec.ro_net_income), lang='ar',)
            amount = "{:.2f}".format(round(rec.ro_net_income, 2))

            split_num = str(amount).split('.')

            pounds = int(split_num[0])
            cents = int(split_num[1])
            lang = 'ar_001'  # ar_001 #en_US
            currency = 'EGP'  # EGP #EUR #JPY #USD

            currency_data = {
                'ar_001': ['مبلغ وقدره', 'و', 'صفر', 'فقط لاغير'],
                'en_US': ['Amount of', 'and', 'zero', 'only'],
                'EGPar_001': ['جنيه مصري', 'قرشاً'],
                'EGPen_US': ['egyptian Pound', 'piastre'],
                'USDar_001': ['دولار أمريكي', 'سنت'],
                'USDen_US': ['american dollar', 'cent'],
                'EURar_001': ['يورو اوروبي', 'سنت'],
                'EURen_US': ['european euro', 'cent'],
                'JPYar_001': ['ين ياباني', 'سن'],
                'JPYen_US': ['japanese yen', 'sen'],
                'SARen_US': ['riyal', 'halalas'],
                'TRYar_001': ['ليرا', 'كوروس'],
                'TRYen_US': ['Lira', 'Kurus'],
           }

            real_in_words = num2words(float(pounds), lang=lang)
            helala_in_words = num2words(float(cents), lang=lang)

            total_in_words = "%s " % currency_data.get(lang)[0]+real_in_words + " %s" % currency_data.get(currency+lang)[0] +\
                ((" %s " % currency_data.get(lang)[1] + helala_in_words + " %s" % currency_data.get(currency+lang)[1])
                 if helala_in_words != '%s' % currency_data.get(lang)[2] else "") + " %s" % currency_data.get(lang)[3]
            
            # ro_numinwords = ('مبلغ وقدره {} جنيه مصري فقط لاغير').format(ro_numinwords)
            rec.ro_numinwords = total_in_words


