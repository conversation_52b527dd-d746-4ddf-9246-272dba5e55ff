# -*- coding: utf-8 -*-

from odoo import api,fields,models,_


class AccountAnalyticPlan(models.Model):
    _inherit = "account.analytic.plan"
 
    is_def = fields.Boolean(string='Is Default', default=False)
 

class StockPicking(models.Model):
    _name = 'stock.picking'
    _inherit = ['stock.picking', 'analytic.mixin']
    
    plan_id = fields.Many2one('account.analytic.plan',string='Project')

    analytic_account_id = fields.Many2one('account.analytic.account',
        string="Analytic Account",
        copy=False, check_company=True,  # Unrequired company
        domain="[('plan_id','=',plan_id), '|', ('company_id', '=', False), ('company_id', '=', company_id)]")

    # === Analytic fields === #
    analytic_line_ids = fields.One2many(
        comodel_name='account.analytic.line', inverse_name='picking_id',
        string='Analytic lines',
    )
    
    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if self.env['stock.picking.type'].browse(vals.get('picking_type_id')).code == 'outgoing':
                vals['plan_id'] = self.env['account.analytic.plan'].search([('is_def' , '=' , True)]).id
        return super(StockPicking,self).create(vals_list)


class StockMove(models.Model):
    _inherit = "stock.move"

    def _generate_valuation_lines_data(self, partner_id, qty, debit_value, credit_value, debit_account_id, credit_account_id, svl_id, description):
        
        result = super(StockMove, self)._generate_valuation_lines_data(partner_id, qty, debit_value, credit_value, debit_account_id, credit_account_id, svl_id, description)
        if self.picking_id.picking_type_code == 'outgoing' and self.picking_id.analytic_account_id:
            result['debit_line_vals']['account_id'] = self.product_id.categ_id.property_account_expense_categ_id.id or result['debit_line_vals']['account_id']
            # result['debit_line_vals']['analytic_account_id'] = self.picking_id.analytic_account_id.id or False
            result['debit_line_vals']['analytic_distribution'] = {self.picking_id.analytic_account_id.id: 100}
            # self.picking_id.analytic_distribution or False

        if self.picking_id.picking_type_code == 'incoming' and self.picking_id.analytic_account_id:
            result['credit_line_vals']['account_id'] = self.product_id.categ_id.property_account_expense_categ_id.id or result['credit_line_vals']['account_id']
            # result['credit_line_vals']['analytic_account_id'] = self.picking_id.analytic_account_id.id or False
            result['credit_line_vals']['analytic_distribution'] = {self.picking_id.analytic_account_id.id: 100}
            # self.picking_id.analytic_distribution or False

        return result

class StockReturnPicking(models.TransientModel):
    _inherit = 'stock.return.picking'

    def _create_returns(self):
        
        new_picking, pick_type_id = super(StockReturnPicking, self)._create_returns()
        picking = self.env['stock.picking'].browse(new_picking)
        origin_picking = self.env['stock.picking'].search([('name','=',picking.origin.replace('Return of ',''))])
        picking.write({
            'plan_id': origin_picking.plan_id,
            'analytic_account_id': origin_picking.analytic_account_id,
            })
        return new_picking, pick_type_id
        