<odoo>
  <data>
    <record id="view_picking_form_analytic_account" model="ir.ui.view">
      <field name="model">stock.picking</field>
      <field name="inherit_id" ref="stock.view_picking_form"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='origin']" position="after">
            <field name="plan_id" attrs="{'readonly': [('state','=','done')]}"  force_save="1"/>
            <!-- groups="analytic.group_analytic_accounting" -->
            <field name="analytic_account_id" context="{'default_plan_id':plan_id, 'default_name':name}" attrs="{'readonly': [('state','=','done')], 'invisible': [('picking_type_code', 'not in', ('outgoing','incoming'))], 'required': [('picking_type_code', 'in', ('outgoing')),('sale_id','=',False)]}"  force_save="1"/>

            <!-- <field name="analytic_distribution" widget="analytic_distribution"
                    options="{'force_applicability': 'optional', 'disable_save': true}" attrs="{'invisible': [('picking_type_code', 'not in', ('outgoing','incoming'))], 'required': [('picking_type_code', 'in', ('outgoing')),('sale_id','=',False)]}"/> -->
            <field name="sale_id" invisible="1"/>  
          </xpath>   
      </field>
    </record>

    <record id="account_analytic_plan_form_view_inherit_add_flag" model="ir.ui.view">
      <field name="name">account.analytic.plan.view.form.inherit</field>
      <field name="model">account.analytic.plan</field>
      <field name="inherit_id" ref="analytic.account_analytic_plan_form_view"/>
      <field name="arch" type="xml">
        <xpath expr="//group/group/field[@name='color']" position="after">
          <field name="is_def" />
        </xpath>
      </field>
    </record>

  </data>
</odoo>