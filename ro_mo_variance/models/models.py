# -*- coding: utf-8 -*-

from odoo import models, fields, api,_, SUPERUSER_ID

from odoo.osv import expression
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.tools.float_utils import float_compare, float_is_zero, float_round
from odoo.exceptions import UserError

class StockMove(models.Model):
    _inherit='stock.move'

    variance_qty = fields.Float(compute="get_variance_qty")
    
    @api.depends('should_consume_qty','quantity_done')
    def get_variance_qty(self):
        for rec in self:
            # if rec.state == 'done':
            #     if not rec.variance_qty: 
            #        rec.variance_qty = 0 
            #     return
            factor = (rec.raw_material_production_id.qty_producing/rec.raw_material_production_id.bom_id.product_qty) if rec.raw_material_production_id.bom_id.product_qty else 0
            rec.variance_qty = (rec.bom_line_id.product_qty * factor) - rec.quantity_done

            if rec.bom_line_id.product_qty * factor != rec.product_uom_qty and rec.quantity_done == 0:
                rec.variance_qty = 0