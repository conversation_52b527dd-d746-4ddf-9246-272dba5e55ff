from odoo import models, fields, api
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    edit_pricelist_group = fields.Bo<PERSON>an(
        compute="_compute_edit_pricelist_group",
    )

    @api.depends('partner_id','team_id','user_id','date_order')
    def _compute_edit_pricelist_group(self):
        for record in self:
            if self.env.user.has_group('edit_pricelist_group.pricelist_group'):
                record.edit_pricelist_group = True
            else:
                record.edit_pricelist_group = False
    @api.onchange('partner_id','team_id','user_id','date_order')
    def _compute_edit_pricelist_group(self):
        for record in self:
            if self.env.user.has_group('edit_pricelist_group.pricelist_group'):
                record.edit_pricelist_group = True
            else:
                record.edit_pricelist_group = False
