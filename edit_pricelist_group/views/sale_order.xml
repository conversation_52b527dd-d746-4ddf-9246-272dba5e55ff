<odoo>
    <record id="add_security_group_pricelist" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='pricelist_id']" position="after">
                <field name="edit_pricelist_group" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='pricelist_id']" position="attributes">
                <attribute name="attrs">{'readonly': ['|',('edit_pricelist_group', '=', False),('state', '!=', 'draft')]}</attribute>
            </xpath>
        </field>
    </record>
</odoo>