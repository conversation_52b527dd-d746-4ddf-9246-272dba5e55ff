from odoo import _, fields, models, api
from odoo.exceptions import UserError, ValidationError


class ReturnPicking(models.TransientModel):
    _inherit = 'stock.return.picking'
    
    def _create_returns(self):
        for return_line in self.product_return_moves:
            # print("anas3")
            # print(return_line)
            # print(return_line.quantity)
            # print(return_line.quantity_reference)
            if return_line.quantity < 0.0:
                raise UserError(_(f"You can't return negative quantity for product {return_line.product_id.name}"))
            elif return_line.quantity_reference < 0.0:
                raise UserError(_(f"the delivered is negative for product {return_line.product_id.name}"))
            elif return_line.quantity > return_line.quantity_reference:
                raise UserError(_(f"You can't return more quantity than ordered for product {return_line.product_id.name} availible: {return_line.quantity_reference}"))
         
            
        res = super(ReturnPicking, self)._create_returns()
        return res
    
    
    @api.model
    def _prepare_stock_return_picking_line_vals_from_move(self, stock_move):
        vals = super(ReturnPicking, self)._prepare_stock_return_picking_line_vals_from_move(stock_move)
        vals['quantity_reference'] = vals['quantity']
        return vals