# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    product_categ_id = fields.Many2one(related='product_tmpl_id.categ_id', store=True)

    def action_inventory_report_domain(self):
        locations = self.env['stock.location'].search([('usage','=', 'internal')])
        current_user = self.env.user

        user_locations = locations.filtered(lambda x: current_user in x.location_user_ids)
                                            # warehouse_id.crm_team_id.member_ids)

        action = self.env["ir.actions.actions"]._for_xml_id("product_onhand_view.action_inventory_report_view")

        if not self.env.user.has_group('base.group_system'):
                action['domain'] = [('location_id', 'in', user_locations.ids),('on_hand','=',True)]

        return action