# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class PRDepartment(models.Model):
    _name = 'pr.department'
    _description = 'PR Department'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    name = fields.Char('Name', required=True, copy=False)
    
    first_level = fields.Many2many('res.users', 'frst_level_res_user_rel', 'department_id', 'user_id')
    sec_level = fields.Many2many('res.users', 'scnd_level_res_user_rel', 'department_id', 'user_id')
    third_level = fields.Many2many('res.users', 'thrd_level_res_user_rel', 'department_id', 'user_id')

    foreign_third_level = fields.Many2many('res.users', 'frgn_thrd_level_res_user_rel', 'department_id', 'user_id')
    allowed_users = fields.Many2many('res.users', 'department_allowed_users_rel', 'department_id', 'user_id', string='Allowed Users')