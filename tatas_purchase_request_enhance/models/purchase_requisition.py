# -*- coding: utf-8 -*-

from collections import defaultdict
from datetime import datetime, time
from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import UserError
from odoo.addons.purchase_requisition.models import purchase_requisition

PURCHASE_REQUISITION_STATES = [
    ('draft', 'First Approval'),
    ('manager_apprv', 'Manager Approval'),
    ('admin_apprv', 'Purchasing Approval'),
    ('ongoing', 'Ongoing'),
    ('in_progress', 'Confirmed'),
    ('open', 'Bid Selection'),
    ('done', 'Closed'),
    ('cancel', 'Cancelled')
]

class PurchaseRequisition(models.Model):
    _inherit = "purchase.requisition"

    state = fields.Selection(PURCHASE_REQUISITION_STATES)

    state_blanket_order = fields.Selection(PURCHASE_REQUISITION_STATES)
    
    currency_id = fields.Many2one('res.currency', 'Currency', required=True, default=False)
    
    project_id = fields.Many2one('account.analytic.account',copy=False, check_company=True,  # Unrequired company
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")

    pr_type = fields.Selection([('local','Local'),('foreign','Foreign')], 'PR Type')

    purchase_type = fields.Selection([('project','Project'),('regular','Regular')])

    note = fields.Text()

    amount_total = fields.Float(string='Total', store=True, readonly=True, compute='_amount_all')

    is_third_level = fields.Boolean(compute="get_allowed_in_third")
    def get_allowed_in_third(self):
        for rec in self:
            rec.is_third_level = False
            if rec.state == 'admin_apprv' and self.env.user in rec.department_id.third_level:
                rec.is_third_level = True

    # employee_id = fields.Many2one('hr.employee', compute="get_representative_emp")
    # @api.depends('user_id')
    # def get_representative_emp(self):
    #     for this in self:
    #         this.employee_id = False
    #         if this.user_id:
    #             employee_rec = self.env['hr.employee'].search([('user_id', '=', this.user_id.id)])
    #             if employee_rec:
    #                 this.employee_id = employee_rec[-1]
    # department_id = fields.Many2one('hr.department', related='employee_id.department_id')
    department_id = fields.Many2one('pr.department')


    def action_draft(self):
        self.ensure_one()
        # self.name = 'New'
        self.write({'state': 'draft'})


    @api.onchange('vendor_id')
    def _onchange_vendor(self):
        self = self.with_company(self.company_id)
        # if not self.vendor_id:
        #     self.currency_id = self.env.company.currency_id.id
        # else:
        #     self.currency_id = self.vendor_id.property_purchase_currency_id.id or self.env.company.currency_id.id

        requisitions = self.env['purchase.requisition'].search([
            ('vendor_id', '=', self.vendor_id.id),
            ('state', '=', 'ongoing'),
            ('type_id.quantity_copy', '=', 'none'),
            ('company_id', '=', self.company_id.id),
        ])
        if any(requisitions):
            title = _("Warning for %s", self.vendor_id.name)
            message = _("There is already an open blanket order for this supplier. We suggest you complete this open blanket order, instead of creating a new one.")
            warning = {
                'title': title,
                'message': message
            }
            return {'warning': warning}

    def action_member_confirm(self):
        for rec in self:
            if rec.department_id and self.env.user in rec.department_id.first_level:
                rec.state = 'manager_apprv'
            else:
                raise UserError("You need to be in the department First Level Approvers to confirm")

    def action_manager_confirm(self):
        for rec in self:
            # employee_rec = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)])
            # if employee_rec and employee_rec[-1] == rec.department_id.manager_id:
            if rec.department_id and self.env.user in rec.department_id.sec_level:
                rec.state = 'admin_apprv'
            else:
                raise UserError("You need to be the department Second Level to confirm")
    @api.model
    def create(self, values):
        if values.get('name', _("New")) == _("New"): 
            values['name'] = self.env['ir.sequence'].next_by_code('purchase.requisition.blanket.order')
        return super().create(values)

    def action_in_progress(self):
        self.ensure_one()
        if self.pr_type == 'local' and self.department_id and self.env.user in self.department_id.third_level:
            return super(PurchaseRequisition, self).action_in_progress()
        elif self.pr_type == 'foreign' and self.department_id and self.env.user in self.department_id.foreign_third_level:
            return super(PurchaseRequisition, self).action_in_progress()
        else:
            raise UserError("You need to be the department Third Level to confirm")

    def action_create_po(self):
        # Create RFQ
        rfq_env = self.env['purchase.order']
        purchase_values_by_partner = defaultdict(list)

        for line in self.line_ids:
            partner = line.vendor_id or line.requisition_id.vendor_id

            if not partner or line.qty_ordered > 0:
                continue

            FiscalPosition = self.env['account.fiscal.position']
            fpos = FiscalPosition.with_company(
                self.company_id)._get_fiscal_position(partner)
            payment_term = partner.property_supplier_payment_term_id
            
            name = '' 
            if line.product_description_variants:
                name += '\n' + line.product_description_variants
            else:
                name = line.product_id.display_name
                if line.product_id.description_purchase:
                    name += '\n' + line.product_id.description_purchase

            if line.schedule_date:
                date_planned = datetime.combine(line.schedule_date, time.min)
            else:
                date_planned = datetime.now()

            # Compute taxes
            taxes_ids = fpos.map_tax(line.product_id.supplier_taxes_id.filtered(
                lambda tax: tax.company_id == self.company_id)).ids

            # Compute quantity and price_unit
            if line.product_uom_id != line.product_id.uom_po_id:
                product_qty = line.product_uom_id._compute_quantity(
                    line.product_qty, line.product_id.uom_po_id)
                price_unit = line.product_uom_id._compute_price(
                    line.price_unit, line.product_id.uom_po_id)
            else:
                product_qty = line.product_qty
                price_unit = line.price_unit

            if self.type_id.quantity_copy != 'copy':
                product_qty = 0

            order_line = [(0, 0, {
                'name': name,
                'product_id': line.product_id.id,
                'product_uom': line.product_id.uom_po_id.id,
                'product_qty': product_qty,
                'price_unit': price_unit,
                'taxes_id': [(6, 0, taxes_ids)],
                'date_planned': date_planned,
                'analytic_distribution': line.analytic_distribution,
                # 'move_dest_ids': line.move_dest_id and [(4, line.move_dest_id.id)] or [],
            })]

            if len(purchase_values_by_partner[partner.id]) > 0:
                purchase_values_by_partner[partner.id][0]['order_line'].append(
                    order_line[0])

            else:
                values = {
                    'requisition_id': self.id,
                    'user_id': False,
                    'fiscal_position_id': fpos.id,
                    'payment_term_id': payment_term.id,
                    'company_id': self.company_id.id,
                    'currency_id': self.currency_id.id,
                    'notes': self.description,
                    'date_order': fields.Datetime.now(),
                    'partner_id': partner.id,
                    'origin': self.name,
                    'picking_type_id': self.picking_type_id.id,
                    'order_line': order_line
                }

                purchase_values_by_partner[partner.id].append(
                    values)
        
        for origin, purchase_values in purchase_values_by_partner.items():
            
            purchase_order = rfq_env.create(purchase_values)
            #purchase_order.button_confirm()

        # if sum(self.line_ids.mapped('qty_ordered')) == sum(self.line_ids.mapped('product_qty')):
        #     self.action_open()
        #     self.action_done()


    @api.onchange('vendor_id')
    def update_vendor_lines(self):
        self.line_ids.vendor_id = self.vendor_id

    @api.depends('line_ids.price_total')
    def _amount_all(self):
        for order in self:
            order.amount_total = sum(order.line_ids.mapped('price_total'))

class PurchaseRequisitionLine(models.Model):
    _inherit = 'purchase.requisition.line'

    vendor_id = fields.Many2one('res.partner', string="Vendor", domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")

    price_total = fields.Float(compute='_compute_amount', string='Total', store=True)

    qty_received = fields.Float(compute='_compute_received_qty', string='Received Quantities')

    @api.depends('product_qty', 'price_unit')
    def _compute_amount(self):
        for line in self:

            line.update({
                'price_total': line.product_qty * line.price_unit,
            })


    @api.onchange('product_id')
    def _onchange_product_id_name(self):
        for rec in self:
            if rec.product_id:
                rec.product_description_variants = rec.product_id.get_product_multiline_description_sale()
            else:
                rec.product_description_variants = ""

    # @api.depends('requisition_id.purchase_ids.state')
    def _compute_received_qty(self):
        line_found = set()
        for line in self:
            total = 0.0
            for po in line.requisition_id.purchase_ids.filtered(lambda purchase_order: purchase_order.state in ['purchase', 'done']):
                for po_line in po.order_line.filtered(lambda order_line: order_line.product_id == line.product_id):
                    if po_line.product_uom != line.product_uom_id:
                        total += po_line.product_uom._compute_quantity(po_line.qty_received, line.product_uom_id)
                    else:
                        total += po_line.qty_received
            if line.product_id not in line_found:
                line.qty_received = total
                line_found.add(line.product_id)
            else:
                line.qty_received = 0