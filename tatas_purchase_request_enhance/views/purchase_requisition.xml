<odoo>
    <data>

        <record id="view_purchase_requisition_search_inherit" model="ir.ui.view">
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_filter"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='product_id']" position="after">
                    <field name="vendor_id" />
                    <field name="project_id" />
                </xpath>

            </field>
        </record>

        <record id="view_purchase_requisition_form_inherit" model="ir.ui.view">
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_form"/>
            <field name="arch" type="xml">
               
                <xpath expr="//field[@name='ordering_date']" position="attributes">
                    <attribute name="required">1</attribute>          
                </xpath>

                <xpath expr="//button[@name='action_cancel']" position="attributes">
                    <attribute name="states">draft,manager_apprv,admin_apprv,in_progress,ongoing</attribute>
                    <attribute name="groups">tatas_purchase_request_enhance.purchase_request_cancel</attribute>
                </xpath>
                
                   
                <xpath expr="//button[@name='%(purchase_requisition.action_purchase_requisition_to_so)d'][2]" position="replace"/>
                <xpath expr="//button[@name='%(purchase_requisition.action_purchase_requisition_to_so)d'][1]" position="replace">
                    <button name="action_create_po" states="in_progress" string="Create RFQs" type="object" class="btn-primary"/>
                    
                    <button name="action_member_confirm" states="draft" string="First Approval" type="object" class="btn-primary"/>
                    <button name="action_manager_confirm" states="manager_apprv" string="Manager Approval" type="object" class="btn-primary"/>

                </xpath>

                <xpath expr="//button[@name='action_in_progress']" position="attributes">
                    <attribute name="states" >admin_apprv</attribute>
                    <attribute name="string" >Purchasing Approval</attribute>
                    <!-- <attribute name="groups" >purchase.group_purchase_manager</attribute> -->
                </xpath>

                <xpath expr="//field[@name='type_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='user_id']" position="after">
                    <!-- <field name="employee_id" invisible="1"/> -->
                    <field name="department_id" required="1" attrs="{'readonly':[('state', '!=', 'draft')]}" options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"/>
                </xpath>

                <xpath expr="//group//field[@name='currency_id']" position="after">
                    <field name="project_id" options="{'no_create':True, 'no_create_edit': True, 'no_open': True}"/>
                    
                    <field name="purchase_type" required="1"/>
                    <field name="pr_type" required="1"/>
                    <field name="is_third_level" invisible="1"/>
                </xpath>

                <xpath expr="//notebook" position="before">
                    <field name="note" placeholder="Note....."/>
                </xpath>

                <xpath expr="//field[@name='line_ids']" position="attributes">
                    <attribute name="attrs">{'readonly':['|', ('state', 'in', ['in_progress','ongoing','open','done']),'&amp;', ('is_third_level','!=',True),('state','=','admin_apprv')]}</attribute>
                </xpath>

                <xpath expr="//field[@name='line_ids']/tree/field[@name='product_id']" position="attributes">
                    <attribute name="attrs">{'readonly':[('parent.state', 'in', ['in_progress'])]}</attribute>
                </xpath>
                <xpath expr="//field[@name='line_ids']/tree/field[@name='product_qty']" position="attributes">
                    <attribute name="attrs">{'readonly':[('parent.state', 'in', ['in_progress'])]}</attribute>
                </xpath>
                <xpath expr="//field[@name='line_ids']/tree/field[@name='product_uom_id']" position="attributes">
                    <attribute name="attrs">{'readonly':[('parent.state', 'in', ['in_progress'])]}</attribute>
                </xpath>

                <xpath expr="//field[@name='line_ids']/tree/field[@name='product_uom_id']" position="before">
                    <field name="qty_received" optional="show"/>
                </xpath>


                <xpath expr="//field[@name='line_ids']" position="after">
                    <group>
                        <group>
                        </group>
                        <group class="oe_subtotal_footer oe_right">
                            <field name="amount_total"  colspan="2" readonly="1"/>
                        </group>
                    </group>
                    <div class="clearfix"/>

                </xpath>



                <xpath expr="//field[@name='line_ids']/tree/field[@name='product_description_variants']" position="after">
                    <field name="vendor_id" />
                    <field name="company_id" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='line_ids']/tree/field[@name='price_unit']" position="after">
                    <field name="price_total" />
                </xpath>
            </field>
        </record>



        <record id="action_purchase_request_window" model="ir.actions.act_window">
            <field name="name">Purchase Request</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.requisition</field>
            <field name="view_mode">tree,kanban</field>
            <field name="search_view_id" ref="purchase_requisition.view_purchase_requisition_filter"/>
            <field name="view_ids"
                eval="[(5, 0, 0),
                        (0, 0, {'view_mode': 'tree', 'view_id': ref('purchase_requisition.view_purchase_requisition_tree')}),
                        (0, 0, {'view_mode': 'form', 'view_id': ref('purchase_requisition.view_purchase_requisition_form')})]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Start a new purchase agreement
                </p><p>
                An example of a purchase agreement is a blanket order.
                <br/> For a blanket order, you can record an agreement for a specific period
                (e.g. a year) and you order products within this agreement to benefit
                from the negotiated prices.
                </p>
            </field>
        </record>

        <record model="ir.actions.act_window" id="purchase_requisition.action_purchase_requisition_to_so">
            <field name="context">{
                "default_requisition_id":active_id,
                "default_user_id": False,
                "create": False
                }
            </field>
        </record>
    
        <record model="ir.actions.act_window" id="purchase_requisition.action_purchase_requisition_list">
            <field name="context">{
                "default_requisition_id":active_id,
                "default_user_id": False,
                "create": False
                }
            </field>
        </record>

        <menuitem id="menu_purchase_request_root"
                    name="Purchase Request"
                    web_icon="fa fa-shopping-cart,#FFFFFF,#34495e"
                    sequence="15" groups="tatas_purchase_request_enhance.group_purchase_request_user"/>


        <menuitem id="menu_purchase_request_config"
                    name="Configuration"
                    parent="tatas_purchase_request_enhance.menu_purchase_request_root"
                    sequence="100" groups="tatas_purchase_request_enhance.group_purchase_request_manager"/>

        <menuitem id="menu_purchase_request_orders"
                    name="Purchase Request"
                    action="action_purchase_request_window"
                    parent="tatas_purchase_request_enhance.menu_purchase_request_root"
                    sequence="1"/>

    </data>
</odoo>