<odoo>
  <data>
    
    
    <record id="view_pr_department_tree" model="ir.ui.view">
      <field name="name">Purchase Request Department</field>
      <field name="model">pr.department</field>
      <field name="arch" type="xml">
        <tree string="Statements">
          <field name="name"/>
          <field name="first_level" widget="many2many_tags"/>
          <field name="sec_level" widget="many2many_tags"/>
          <field name="third_level" widget="many2many_tags"/>
        </tree>
      </field>
    </record>

    <record id="view_pr_department_form" model="ir.ui.view">
      <field name="name">Purchase Request Department</field>
      <field name="model">pr.department</field>
      <field name="arch" type="xml">
        <form>
          <sheet>
            <div>
              <h1>
                  <field name="name" placeholder="Name..."/>
              </h1>
            </div>

            <group>
              <group>
                <field name='first_level' widget="many2many_tags"/>
                <field name='sec_level' widget="many2many_tags"/>
              </group>
               <group>
                        <!-- Add the new field here -->
                        <field name='allowed_users' widget="many2many_tags"/>
              </group>
              <group>
              </group>
            </group>
            <group>
              <group>
                <field name='third_level' widget="many2many_tags"/>
              </group>
              <group>
                <field name='foreign_third_level' widget="many2many_tags"/>
              </group>
            </group>



          </sheet>
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
            <field name="activity_ids" widget="mail_activity"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>
      </field>
    </record>

    
    <!-- Purchase Request Department -->
    <record id="pr_department_action_window" model="ir.actions.act_window">
      <field name="name">Purchase Request Department</field>
      <field name="res_model">pr.department</field>
      <field name="type">ir.actions.act_window</field>
      <field name="view_mode">tree,form</field>
      <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
          Create a Purchase Request Department Order
        </p>
      </field>
    </record>    
    <menuitem action="pr_department_action_window" id="pr_department_menu" name="PR Department" sequence="30" parent="tatas_purchase_request_enhance.menu_purchase_request_config"/>
    

  </data>
</odoo>