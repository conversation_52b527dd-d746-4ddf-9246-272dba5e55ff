<?xml version="1.0" encoding="utf-8"?>
<odoo>
     <data>
        <record id="rule_pr_department_access" model="ir.rule">
            <field name="name">PR Department: Access Own Departments</field>
            <field name="model_id" ref="purchase_requisition.model_purchase_requisition"/>
            <field name="domain_force">[('department_id.allowed_users', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('tatas_purchase_request_enhance.group_purchase_request_on_department'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="rule_department_type_access" model="ir.rule">
            <field name="name">Department Access Own Departments</field>
            <field name="model_id" ref="tatas_purchase_request_enhance.model_pr_department"/>
            <field name="domain_force">[('allowed_users', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('tatas_purchase_request_enhance.group_purchase_request_on_department'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>

</odoo>

  
    
 
