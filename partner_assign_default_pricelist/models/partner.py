# -*- coding: utf-8 -*-

from odoo import models, fields, api, _

class ResPartner(models.Model):
    _inherit = 'res.partner'


    @api.depends('country_id')
    @api.depends_context('company')
    def _compute_product_pricelist(self):
        res = super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self)._compute_product_pricelist()
        for rec in self:
            if rec.property_product_pricelist:
                rec._inverse_product_pricelist()
        return res


 