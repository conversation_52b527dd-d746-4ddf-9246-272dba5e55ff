<odoo>
    <data>
        <record id="return_validation_category" model="ir.module.category">
            <field name="name">Validate Returned Orders</field>
        </record>
        <record id="return_validation_group" model="res.groups">
            <field name="name">Validate Returned Orders</field>
            <field name="category_id" ref="custom_return_validation.return_validation_category" />
        </record>
    </data>
</odoo>