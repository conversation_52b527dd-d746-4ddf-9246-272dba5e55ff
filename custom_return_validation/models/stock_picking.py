# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.exceptions import UserError


class StockPicking(models.Model):
    _inherit = "stock.picking"

    is_returned_order = fields.Boolean(
        string="Is Returned Order",
    )

    is_manager = fields.Boolean(
        compute="_check_user_group",
        default=lambda self: self.env.user.has_group(
            "custom_return_validation.return_validation_group"
        ),
    )

    def _check_user_group(self):
        self.is_manager = self.env.user.has_group(
            "custom_return_validation.return_validation_group"
        )

    # @api.constrains("state")
    # def _check_is_returned_order(self):
    #     for picking in self:
    #         if picking.is_returned_order and picking.state == "done":
    #             raise UserError("You cannot validate a returned order.")
