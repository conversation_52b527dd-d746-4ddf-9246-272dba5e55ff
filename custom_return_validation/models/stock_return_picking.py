# -*- coding: utf-8 -*-
from odoo import api, models, fields
from odoo.exceptions import UserError


class ReturnPicking(models.TransientModel):
    _inherit = "stock.return.picking"

    def _create_returns(self):
        new_picking_id, picking_type_id = super(ReturnPicking, self)._create_returns()

        # Update the new picking's custom field 'is_returned_order' to True
        new_picking = self.env["stock.picking"].browse(new_picking_id)
        new_picking.is_returned_order = True

        return new_picking_id, picking_type_id
