<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_picking_form_inherited" model="ir.ui.view">
        <field name="name">stock.picking.form.inherited</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='origin']" position="after">
                <field name="is_returned_order" invisible="1"  widget="boolean_toggle"
                />
                <field name="is_manager" invisible="1" widget="boolean_toggle"
                />
            </xpath>
            <xpath expr="//header/button[@name='button_validate' and contains(@class, 'oe_highlight')]" position="attributes">
                <attribute name="attrs">{'invisible': ['|', '|', ('state', 'in', ('waiting','confirmed')), ('show_validate', '=', False), '&amp;', ('is_manager', '=', False), ('is_returned_order', '=', True)]}</attribute>
            </xpath>
            <xpath expr="//header/button[@name='button_validate' and contains(@class, 'o_btn_validate')]" position="attributes">
                <attribute name="attrs">{'invisible': ['|', '|', ('state', 'not in', ('waiting', 'confirmed')), ('show_validate', '=', False), '&amp;', ('is_manager', '=', False), ('is_returned_order', '=', True)]}</attribute>
            </xpath>

        </field>
    </record>
</odoo>