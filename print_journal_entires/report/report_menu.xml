<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="journal_entries_report_id">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <!--			<link rel='stylesheet' href="/journal_entries_print/static/src/css/report_css.css"/>-->
                <t t-foreach="docs" t-as="o">
                    <div class="page" style="font-size:15px;">
                        <div>
                            <h3>
                                <span t-field="o.name"/>
                            </h3>
                        </div>
                        <br></br>
                        <div class="row">
                            <table width="100%" class="table-bordered">
                                <tr>
                                    <td>Journal:
                                        <span t-field="o.journal_id.name" />
                                    </td>
                                    <td>
	            						Date:
                                        <span t-field="o.date"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
	            						Partner:
                                        <span t-field="o.partner_id.name"/>
                                    </td>
                                    <td>
	            						Reference:
                                        <span t-field="o.ref"/>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="row">
                            <style>
                    .tblbordr {
                        text-align: center;
                        border: 2px solid black;
                    }
                    </style>
                            <br></br>
                            <table width="100%" class="border-collapse:separate;">
                                <thead >
                                    <tr >
                                        <th>Account</th>
                                        <th>code</th>
                                        <th>Partner</th>
                                        <th>Lable</th>
                                        <th>Analytic Account</th>
                                        <th>Debit</th>
                                        <th>Credit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="o.line_ids" t-as="line">
                                        <tr>
                                            <td>
                                                <span t-field="line.account_id.name"/>
                                            </td>
                                            <td>
                                                <span t-field="line.account_id.code"/>
                                            </td>
                                            <td>
                                                <span t-field="line.partner_id.name"/>
                                            </td>
                                            <td>
                                                <span t-field="line.name"/>
                                            </td>
                                            <td>
                                                <span t-field="line.analytic_account_id.name"/>
                                            </td>
                                            <td>
                                                <span t-field="line.debit"/>
                                            </td>
                                            <td>
                                                <span t-field="line.credit"/>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                                <tfooter>
                                    <tr>
                                        <td colspan="5"></td>
                                        <td>
                                            <span t-esc="o.total_debit_credit().get('dr_total')"/>
                                        </td>
                                        <td>
                                            <span t-esc="o.total_debit_credit().get('cr_total')"/>
                                        </td>
                                    </tr>
                                </tfooter>
                            </table>
                        </div>
                    </div>
                </t>
                <table style="border:1px #000 solid;width:100%;text-align:center;margin-top: 16px !important;">
                    <tbody>
						<tr>
                            <td><span style="visibility: hidden;">Hi</span></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>Prepared By</td>
                            <td>Checked By</td>
                            <td>Approved By</td>
                        </tr>
                        <tr>
                            <td><span style="visibility: hidden;">Hi</span></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td><span style="visibility: hidden;">Hi</span></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </t>
        </t>
    </template>
    <record id="journal_entries_moce_print_id" model="ir.actions.report">
        <field name="name">Journal Entries</field>
        <field name="model">account.move</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">print_journal_entires.journal_entries_report_id</field>
        <field name="report_file">print_journal_entires.journal_entries_report_id</field>
        <field name="print_report_name">('Journal - %s' % (object.name))</field>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="binding_type">report</field>
	<field name="groups_id" eval="[(4, ref('print_journal_entires.print_journal_entry'))]"/>
    </record>
</odoo>
