# -*- coding: utf-8 -*-

from odoo import api, fields, models, _, SUPERUSER_ID
import json
from odoo.addons.sale.models.sale_order import SaleOrder


@api.onchange('partner_id')
def _onchange_partner_id_warning(self):
    if not self.partner_id:
        return

    partner = self.with_user(SUPERUSER_ID).partner_id

    # If partner has no warning, check its company
    if partner.sale_warn == 'no-message' and partner.parent_id:
        partner = partner.parent_id

    if partner.sale_warn and partner.sale_warn != 'no-message':
        # Block if partner only has warning but parent company is blocked
        if partner.sale_warn != 'block' and partner.parent_id and partner.parent_id.sale_warn == 'block':
            partner = partner.parent_id

        if partner.sale_warn == 'block':
            self.partner_id = False

        return {
            'warning': {
                'title': _("Warning for %s", partner.name),
                'message': partner.sale_warn_msg,
            }
        }

SaleOrder._onchange_partner_id_warning=_onchange_partner_id_warning

PAYMENT_STATE_SELECTION = [
    ('not_paid', 'Not Paid'),
    ('paid', 'Paid'),
    ('partial', 'Partially Paid')
]


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    payment_ids = fields.One2many(
        string='Payments',
        comodel_name='account.payment',
        inverse_name='sale_order_id',
        tracking=True
    )

    amount_paid_text = fields.Text(string="Amount Paid TXT", store=True,
                                  readonly=True, copy=False, compute='_compute_payment')

    amount_paid = fields.Monetary(currency_field='currency_id', string="Amount Paid", store=True,
                                  readonly=True, copy=False, compute='_compute_payment')

    amount_due = fields.Monetary(currency_field='currency_id', string="Amount Due", store=True,
                                  readonly=True, copy=False, compute='_compute_payment')

    payment_state = fields.Selection(PAYMENT_STATE_SELECTION, string="Payment Status", store=True,
                                     readonly=True, copy=False, tracking=True, compute='_compute_payment')
                                     
    payment_count = fields.Integer("Payments", compute='_compute_payment', store=True,
                                  readonly=True, copy=False)

    
    def action_sale_open_payments(self):
        action = self.env["ir.actions.actions"]._for_xml_id("account.action_account_payments")
        action['domain'] = [('sale_order_id', '=', self.id)]
        return action

    @api.depends('payment_ids','amount_total')
    def _compute_payment(self):
        for order in self:

            if order.payment_ids:
                payment_ids = order.payment_ids

                pay_text = ''
                amount_total = 0

                for payment in payment_ids:

                    if payment.currency_id and payment.currency_id != order.currency_id:
                        amount = payment.currency_id._convert(
                            payment.amount , order.currency_id, order.company_id, payment.date or fields.Date.context_today(order))
                        pay_text += '%s  %s%s/%s%s \n'%(payment.name, payment.amount, payment.currency_id.symbol, amount, order.currency_id.symbol)   
                    else:
                        amount = payment.amount
                        pay_text += '%s  %s%s \n'%(payment.name, payment.amount, payment.currency_id.symbol)
                    
                    amount_total += amount


                amount_due = order.amount_total - amount_total

                if amount_due == 0:
                    order.payment_state = 'paid'
                else:
                    order.payment_state = 'partial'
                
                order.amount_due = amount_due
                order.amount_paid = amount_total
                order.payment_count = len(payment_ids)

                
                    
                order.amount_paid_text = pay_text
            else:
                order.payment_state = 'not_paid'
                order.amount_due = order.amount_total
                order.amount_paid = 0
                order.payment_count = 0
                order.amount_paid_text = ''
                

    def action_register_payment(self):
        return {
            'name': _('Register Payment'),
            'res_model': 'sale.payment.register',
            'view_mode': 'form',
            'context': {
                'active_model': 'sale.order',
                'active_ids': self.ids,
            },
            'target': 'new',
            'type': 'ir.actions.act_window',
        }
