# -*- coding: utf-8 -*-
from collections import defaultdict
from lxml import etree

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class SalePaymentRegister(models.TransientModel):
    _name = 'sale.payment.register'
    _description = 'Register Payment'

    
    journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Journal',
        domain="[('type', 'in', ('bank','cash')),('company_id', '=', company_id)]",
        check_company=True,
    )

    amount = fields.Monetary(currency_field='currency_id')
    amount_due = fields.Monetary(currency_field='currency_id', string="Amount Due", readonly=True)
    amount_due_currency = fields.Monetary(currency_field='sale_currency_id', string="Amount Due", readonly=True)
    amount_diff = fields.Monetary(currency_field='currency_id', string="Amount Diff", compute='_compute_diff')
    sale_currency_id = fields.Many2one(comodel_name='res.currency', related='sale_order_id.currency_id')
    sale_order_id = fields.Many2one(comodel_name='sale.order', string="Sale Order", readonly=True)
    partner_id = fields.Many2one(comodel_name='res.partner', string="Customer", readonly=True)
    company_id = fields.Many2one(comodel_name='res.company', string="Company", readonly=True)

    currency_id = fields.Many2one('res.currency', string='Currency', store=True, readonly=False,
                                  compute='_compute_currency_id',
                                  help="The payment's currency.")    

    payment_date = fields.Date(
        string='Payment Date',
        default=fields.Date.context_today,
    )

    
    @api.onchange('currency_id')
    def _onchange_currency_id(self):
        amount_due = self.amount_due_currency

        if self.currency_id and self.currency_id != self.sale_order_id.currency_id:
            amount_due = self.sale_order_id.currency_id._convert(
                        amount_due, self.currency_id, self.company_id, self.payment_date or fields.Date.context_today(self))

        self.amount = amount_due
        self.amount_due = amount_due
    
    
    @api.model
    def default_get(self, fields_list):
        # OVERRIDE
        res = super().default_get(fields_list)

        if self._context.get('active_model') == 'sale.order':
            sale_order_id = self.env['sale.order'].browse(self._context.get('active_ids', []))
        else:
            raise UserError(_(
                "The register payment wizard should only be called on sale.order records."
            ))

        avaliable_journal_id = self.env['account.journal'].search([
                    ('type', 'in', ('bank', 'cash')),
                    ('company_id', '=', sale_order_id.company_id.id),
                ])

        journal_id = avaliable_journal_id.filtered(lambda journal: journal.currency_id == sale_order_id.currency_id.id or not journal.currency_id)[:1]
        
        res['sale_order_id'] = sale_order_id.id
        res['partner_id'] = sale_order_id.partner_id.id
        res['company_id'] = sale_order_id.company_id.id
        res['amount_due'] = sale_order_id.amount_due
        res['amount_due_currency'] = sale_order_id.amount_due
        res['amount'] = sale_order_id.amount_due
        res['journal_id'] = journal_id
        

        return res

    @api.depends('amount','currency_id')
    def _compute_diff(self):
        for pay in self:
            amount = pay.amount
            amount_due = pay.amount_due
            
            pay.amount_diff = amount_due - amount

    @api.depends('journal_id')
    def _compute_currency_id(self):
        for pay in self:
            pay.currency_id = pay.sale_order_id.currency_id or pay.journal_id.currency_id or pay.journal_id.company_id.currency_id

    def action_create_payments(self):

        if self.amount > self.amount_due:
            raise UserError(_(
                "Amount Is greater than amount due."
            ))

        partner = False
        if self.partner_id and self.partner_id.parent_id:
            partner = self.partner_id.parent_id
        elif self.partner_id:
            partner = self.partner_id

        payment = self.env['account.payment'].create({
            'date': self.payment_date,
            'amount': self.amount,
            'currency_id': self.currency_id.id,
            'journal_id': self.journal_id.id,
            'sale_order_id': self.sale_order_id.id,
            'partner_id': partner.id,
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'ref': self.sale_order_id.name
        })

        payment.action_post()

        return True