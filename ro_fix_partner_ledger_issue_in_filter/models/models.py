# -*- coding: utf-8 -*-

import json

from odoo import models, _, fields
from odoo.exceptions import UserError
from odoo.osv import expression
from odoo.tools.misc import format_date, get_lang

from datetime import timedelta
from collections import defaultdict
from odoo.addons.account_reports.models.account_partner_ledger import PartnerLedgerCustomHandler

# class PartnerLedgerCustomHandlerInherit(models.AbstractModel):

def _dynamic_lines_generator(self, report, options, all_column_groups_expression_totals):
    if self.env.context.get('print_mode') and options.get('filter_search_bar'):
        # Handled here instead of in custom options initializer as init_options functions aren't re-called when printing the report.
        options.setdefault('forced_domain', []).append(('partner_id', 'ilike', options['filter_search_bar']))

    partner_lines, totals_by_column_group = self._build_partner_lines(report, options)
    lines = report._regroup_lines_by_name_prefix(options, partner_lines, '_report_expand_unfoldable_line_partner_ledger_prefix_group', 0)

    # Inject sequence on dynamic lines
    lines = [(0, line) for line in lines]

    # Report total line.
    lines.append((0, self._get_report_line_total(options, totals_by_column_group)))

    return lines

PartnerLedgerCustomHandler._dynamic_lines_generator = _dynamic_lines_generator

def _custom_options_initializer(self, report, options, previous_options=None):
    super(PartnerLedgerCustomHandler,self)._custom_options_initializer(report, options, previous_options=previous_options)
    domain = []

    company_ids = [company_opt['id'] for company_opt in options.get('multi_company', self.env.company)]
    exch_code = self.env['res.company'].browse(company_ids).mapped('currency_exchange_journal_id')
    if exch_code:
        domain += ['!', '&', '&', '&', ('credit', '=', 0.0), ('debit', '=', 0.0), ('amount_currency', '!=', 0.0), ('journal_id', 'in', exch_code.ids)]

    options['forced_domain'] = options.get('forced_domain', []) + domain

    prefix_group_parameter_name = 'account_reports.partner_ledger.groupby_prefix_groups_threshold'
    prefix_groups_threshold = int(self.env['ir.config_parameter'].sudo().get_param(prefix_group_parameter_name, 0))
    if prefix_groups_threshold:
        options['groupby_prefix_groups_threshold'] = prefix_groups_threshold

PartnerLedgerCustomHandler._custom_options_initializer = _custom_options_initializer