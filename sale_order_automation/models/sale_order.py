from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError

class SaleOrder(models.Model):
    _inherit = "sale.order"

    def action_confirm_deliver(self):
        for order in self:

            order.action_confirm()

            warehouse = order.warehouse_id
            if warehouse.is_delivery_set_to_done and order.picking_ids: 
                picking_ids = self.picking_ids.filtered(lambda picking: picking.state not in ['done','cancel'])

                for picking in picking_ids:

                    picking.action_confirm()
                    picking.action_assign()
                    
                    if picking.move_line_ids_without_package:
                        for mv in picking.move_line_ids_without_package:
                            mv.qty_done = mv.reserved_uom_qty
                    else:
                        for mv in picking.move_ids_without_package:
                            mv.quantity_done = mv.product_uom_qty

                    picking.button_validate()
                    

            if warehouse.create_invoice and not order.invoice_ids :
                order._create_invoices()  

            if warehouse.validate_invoice and order.invoice_ids :
                for invoice in order.invoice_ids.filtered(lambda x:x.state == 'draft'):
                    invoice.action_post()
