<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <record model="ir.ui.view" id="sale_confirm_automation_extended_for_warehouse">
            <field name="name">sale.order.extended</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref = "sale.view_order_form"/>
            <field name="arch" type="xml">
                <button name="action_confirm" position="after">
                    <button name="action_confirm_deliver" id="action_confirm_deliver" class="btn-primary" string="confirm &amp; Delivered" type="object" attrs="{'invisible': [('state', 'not in', ['draft'])]}"/>
                </button>

                <xpath expr="//button[@name='action_confirm'][1]" position="attributes">
                    <attribute name="groups">sales_team.group_sale_manager</attribute>
                </xpath>
                <xpath expr="//button[@name='action_confirm'][2]" position="attributes">
                    <attribute name="groups">sales_team.group_sale_manager</attribute>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
