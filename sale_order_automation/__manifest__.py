# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
{
    'name' : 'Sale Order Automation',
    'version' : '16.0',
    'author':'Roaya',
    'category': '',
    'summary': """Enable auto sale workflow with sale order confirmation. Include operations like Auto Create Invoice, Auto Validate Invoice and Auto Transfer Delivery Order.""",
    'description': """
        You can directly create invoice and set done to delivery order by single click
    """,
    'license': 'OPL-1',
    'website': 'https://www.roayadm.com/',
    'depends' : ['base','sale','stock'],
    'data': [
        'views/sale_order.xml',
        'views/stock_warehouse.xml',
    ],

}
