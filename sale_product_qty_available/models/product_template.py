from odoo import models, fields, api
from odoo.exceptions import UserError

class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    assigned_locations_on_hand = fields.Float(string='Locations On Hand', compute="_compute_available_qty")
    def _compute_available_qty(self):
        for rec in self:
            rec.assigned_locations_on_hand = 0
           
            warehouse = self.env.context.get('warehouse')
            if warehouse:
                locations = self.env['stock.location'].search([('location_user_ids','=',self.env.user.id)])
    
                available_qty = rec.with_context({'location' : locations.ids}).qty_available
    
                if locations:
                    rec.assigned_locations_on_hand = available_qty
            else:
                rec.assigned_locations_on_hand = 0

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):        
        products = super(ProductTemplate, self).search(args, offset=offset, limit=None, order=order, count=False)
        if products and self.env.context.get('show_available', False) and self.env.context.get('warehouse', False):
            locations = self.env['stock.location'].search([('location_user_ids','=',self.env.user.id)])
            # variants = products.product_variant_ids.with_context({'location': locations.ids})
            # onhand_variants = variants.filtered(lambda x:x.free_qty>0)
            # onhand_variants.product_tmpl_id
            
            products = products.with_context({'location' : locations.ids}).filtered(lambda x:x.qty_available>0)
            products = products[:limit]
        return len(products) if count else products
   
    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        """
        name search that supports searching by domain
        """
        domain = ['|',['name', operator, name], ['default_code', operator, name]]
        if not args:
            args = []
        else:    
            domain = ['&'] + domain
        state = self.search(domain+args, limit=limit)
        return state.name_get()