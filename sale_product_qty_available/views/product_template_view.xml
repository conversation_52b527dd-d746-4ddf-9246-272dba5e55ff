<?xml version="1.0"?>
<odoo>
    <record id="view_product_template_tree_inherit" model="ir.ui.view">
        <field name="name">product.template.tree.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='standard_price']" position="after">
                <field name="assigned_locations_on_hand" optional="hide"/>
            </xpath>
        </field>
    </record>
    
    <record id="sale_order_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.form.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/tree/field[@name='product_template_id']" position="after">
                <field name="warehouse_id" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='product_template_id']" position="attributes">
                <attribute name="context">{
                    'partner_id': parent.partner_id,
                    'quantity': product_uom_qty,
                    'pricelist': parent.pricelist_id,
                    'uom': product_uom,
                    'company_id': parent.company_id,
                    'default_list_price': price_unit,
                    'default_description_sale': name,
                    'warehouse': warehouse_id,
                    'show_available': True
                }</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/form//field[@name='product_id']" position="attributes">
              <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/form//field[@name='product_id']" position="after">
                <field name="product_template_id" string="Product" attrs="{'readonly': [('product_updatable', '=', False)], 'required': [('display_type', '=', False)], }" context="{'partner_id': parent.partner_id, 'quantity': product_uom_qty, 'pricelist': parent.pricelist_id, 'uom':product_uom, 'company_id': parent.company_id, 'default_list_price': price_unit, 'default_description_sale': name, 'warehouse': warehouse_id,'show_available': True}" options="{'no_open': True,}" domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" widget="sol_product_many2one"/>
            </xpath>
        </field>
    </record>
</odoo>
