import logging

from odoo import models, fields, api
from odoo import tools
from datetime import datetime
from psycopg2 import sql

_logger = logging.getLogger(__name__)
class StockMoveTime(models.Model):

        _name = "stock.move.time"
        _description = "Stock Location Life cycle"
        _auto = False
        _rec_name = 'date'
        # _order = "date asc"
        _order = "idd asc"

        id = fields.Char(
                string='id',
                size=16,
                readonly=True
        )
        idd = fields.Char(
                string='id',
                size=16,
                readonly=True
        )
        date = fields.Datetime(
                string='Date Planned',
                readonly=True
        )
        description = fields.Char(
                string='Description',
                readonly=True
        )
        product_id = fields.Many2one(
                comodel_name='product.product',
                string='Product',
                readonly=True
        )
        categ_id = fields.Many2one('product.category', string='Product Category')
        
        product_tmpl_id = fields.Many2one(
                related='product_id.product_tmpl_id',
                comodel_name='product.template',
                string='Product Template'
        )
        location_id = fields.Many2one(
                comodel_name='stock.location',
                string='Location',
                readonly=True
        )
        location_name_from = fields.Char(
                string='Location ',
                related="location_id.complete_name"
        )
        company_id = fields.Many2one(
                comodel_name='res.company',
                string='Company',
                readonly=True
        )
        qty_on_hand = fields.Integer(
                string='Quantity On Hand',
                readonly=True
        )
        qty_add = fields.Integer(
                string='In',
                readonly=True
        )
        qty_ded = fields.Integer(
                string='Out',
                readonly=True
        )
        qty_current = fields.Integer(
                string='Balance',
                readonly=True,
                compute='_compute_field'
        )

        time_last = fields.Char(
                string='Days',
                readonly=True,
                compute='_computetime_last_field'
        )
        time_last_1=fields.Float(string="Days")

        @api.depends('date')
        def _computetime_last_field(self):
                locations=[]
                products=[]

                stockobj = self.env['stock.move.time']

                for record in self:

                    out_records = stockobj.search([('location_id','=',record.location_id.id),('product_id', '=', record.product_id.id),('idd','>=',record.idd),('id','>',record.id)])
                    if len(out_records)>0:
                        delta = out_records[0].date - record.date
                        record.time_last = delta.days
                        record.time_last_1 = delta.days
                    else:
                        delta = datetime.now() - record.date
                        record.time_last = delta.days
                        record.time_last_1 = delta.days


        available = fields.Float(readonly=True)


        @api.depends('qty_on_hand')
        def _compute_field(self):
                locations=[]
                products=[]

                stockobj = self.env['stock.move.time']

                for record in self:
                        if record.location_id in locations and record.product_id in products:
                                stockobjsearch = stockobj.search([('location_id.id', '=', record.location_id.id),('product_id.id', '=', record.product_id.id),('idd','<=',record.idd),('id','<',record.id)])
                                
                                lastqty = record.qty_on_hand
                                for stockobjsearchline in stockobjsearch: lastqty += stockobjsearchline.qty_on_hand
                                record.qty_current = lastqty
                        else:
                                locations.append(record.location_id)
                                products.append(record.product_id)
                                record.qty_current = record.qty_on_hand

        def _view_internal_add(self):

                view_str = """
                WITH added_row_number AS (

                    select sml.id, sml.id as idd, sml.date, sml.product_id, prt.categ_id, (
                    select sum(quantity) from stock_quant
                    INNER join stock_location l ON
    						stock_quant.location_id=l.id
    						and l.usage in ('internal','transit')
                    where product_id=sml.product_id and stock_quant.company_id = 1
                    ) as available, 
                    sl.id as location_id, sml.reference as description,
                    case when sml.state ='done' then round(sml.qty_done, 4) else 0 end as qty_on_hand,
                    case when sml.state ='done' then round(sml.qty_done, 4) else 0 end as qty_add,
                    0 as qty_ded,
    				sl.company_id,
                    spt.code as move_type,
                    COALESCE(0,0) as time_last_1

                    from stock_move_line sml 
                    INNER join stock_move sm ON sm.id = sml.move_id
                    left join stock_location sll ON
    						sml.location_id=sll.id
                    left join stock_location sl ON
                            sml.location_dest_id = sl.id
                    left join stock_picking sp ON
                            sp.id = sml.picking_id
                    left join stock_picking_type spt ON
                            sp.picking_type_id = spt.id

                left join product_product pr ON
                        sml.product_id = pr.id
                left join product_template prt ON
                        pr.product_tmpl_id = prt.id
                    where sl.usage='internal'
                    and sml.state != 'cancel'
                    and sml.company_id = sl.company_id
                    and sml.qty_done != 0
                    and sml.state ='done'
                    and pr.active = true
                    and sl.company_id =1

                """
                return view_str

        def _view_internal_deduct(self):
                view_str = """
                    select -sml.id, sml.id as idd, sml.date, sml.product_id, prt.categ_id, (
                    select sum(quantity) from stock_quant
                    INNER join stock_location l ON
    						stock_quant.location_id=l.id
    						and l.usage in ('internal','transit')
                    where product_id=sml.product_id and stock_quant.company_id = 1
                    ) as available, 
                    sl.id as location_id, sml.reference as description,
                    case when sml.state ='done' then round(-sml.qty_done, 4) else 0 end as qty_on_hand,
                    0 as qty_add,
                    case when sml.state ='done' then round(sml.qty_done, 4) else 0 end as qty_ded,
                    sl.company_id,
                    spt.code as move_type,
                    COALESCE(0,0) as time_last_1

                    from stock_move_line sml 
                    INNER join stock_move sm ON sm.id = sml.move_id
                    left join stock_location sll ON
    						sml.location_dest_id=sll.id
                    left join stock_location sl ON
    						sml.location_id = sl.id
                    left join stock_picking sp ON
                            sp.id = sml.picking_id
                    left join stock_picking_type spt ON
                            sp.picking_type_id = spt.id

                left join product_product pr ON
                        sml.product_id = pr.id
                left join product_template prt ON
                        pr.product_tmpl_id = prt.id
                    where sl.usage='internal'
                    and sml.state != 'cancel'
                    and sml.company_id = sl.company_id
                    and sml.qty_done != 0
                    and sml.state ='done'
                    and pr.active = true
                    and sl.company_id =1

				order by date desc 
                )
                SELECT
                *
                FROM added_row_number
                WHERE available>0
                """
                return view_str

        @property
        def _table_query(self):
                return '%s union all %s' % (self._view_internal_add(), self._view_internal_deduct())

        # def init(self):
        #         #tools.drop_view_if_exists(self.env.cr, self._table)

        # #        sql ="""CREATE or REPLACE VIEW %s as (
        #         tools.drop_view_if_exists(self.env.cr, self._table)
        #         self.env.cr.execute(sql.SQL("CREATE or REPLACE VIEW {} as ({} union all {})").format(sql.Identifier(self._table), sql.SQL(self._view_internal_add()), sql.SQL(self._view_internal_deduct())))


                # # self.env.cr.execute('DROP TABLE if EXISTS %s' % self._table)

                # sql ="""CREATE TABLE %s as (
                # %s
                # union all
                # %s
                # )""" % (self._table,
                #         self._view_internal_add(),
                #         self._view_internal_deduct()
                #         )
                # self.env.cr.execute(sql)


        @api.model
        def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):

                res = super(StockMoveTime, self).read_group(domain, fields, groupby, offset=offset, limit=limit, orderby=orderby, lazy=lazy)

                for line in res:
                        if '__domain' in line:
                                lines = self.search(line['__domain'])
                                total_qty = sum(lines.mapped('qty_add')) - sum(lines.mapped('qty_ded'))
                                total_qty_add = sum(lines.mapped('qty_add'))
                                total_qty_ded = sum(lines.mapped('qty_ded'))
                                line['qty_current'] = total_qty
                                line['qty_add'] = total_qty_add
                                line['qty_ded'] = total_qty_ded

                return res




class StockMoveTimeSimp(models.Model):
        _name = "stock.move.time.simp"
        _description = "Stock Product Life cycle"
        _auto = False
        _rec_name = 'date'
        _order = "idd asc"

        id = fields.Integer(
                string='id',
                size=16,
                readonly=True
        )
        idd = fields.Integer(
                string='id',
                size=16,
                readonly=True
        )
        date = fields.Datetime(
                string='Date Planned',
                readonly=True
        )
        description = fields.Char(
                string='Description',
                readonly=True
        )
        product_id = fields.Many2one(
                comodel_name='product.product',
                string='Product',
                readonly=True
        )
        categ_id = fields.Many2one('product.category', string='Product Category')

        product_tmpl_id = fields.Many2one(
                related='product_id.product_tmpl_id',
                comodel_name='product.template',
                string='Product Template'
        )
        location_id = fields.Many2one(
                comodel_name='stock.location',
                string='Location',
                readonly=True
        )
        location_name_from = fields.Char(
                string='Location ',
                related="location_id.complete_name"
        )
        company_id = fields.Many2one(
                comodel_name='res.company',
                string='Company',
                readonly=True
        )
        qty_on_hand = fields.Integer(
                string='Quantity On Hand',
                readonly=True
        )
        qty_add = fields.Integer(
                string='In',
                readonly=True
        )
        qty_ded = fields.Integer(
                string='Out',
                readonly=True
        )
        qty_current = fields.Integer(
                string='Balance',
                readonly=True,
                compute='_compute_field'
        )
        @api.depends('qty_on_hand')
        def _compute_field(self):
                locations=[]
                products=[]
                stockobj = self.env['stock.move.time.simp']

                for record in self:
                        if record.location_id in locations and record.product_id in products:
                                stockobjsearch = stockobj.search([('location_id.id', '=', record.location_id.id),('product_id.id', '=', record.product_id.id),('idd','<',record.idd)])
                                lastqty = record.qty_on_hand
                                for stockobjsearchline in stockobjsearch: lastqty += stockobjsearchline.qty_on_hand
                                record.qty_current = lastqty
                        else:
                                locations.append(record.location_id)
                                products.append(record.product_id)
                                record.qty_current = record.qty_on_hand

        # time_last = fields.Char(
        #         string='Days',
        #         readonly=True,
        #         compute='_computetime_last_field'
        # )
        time_last_1=fields.Float(string="Days")

        # @api.depends('date')
        # def _computetime_last_field(self):
        #         stockobj = self.env['stock.move.time.simp']
        #         for record in self:

        #                 out_records = stockobj.search([('location_id','=',record.location_id.id),('product_id', '=', record.product_id.id),('idd','>',record.idd)])
        #                 if len(out_records)>0:
        #                         delta = out_records[0].date - record.date
        #                         record.time_last = delta.days
        #                         record.time_last_1 = delta.days
        #                 else:
        #                         delta = datetime.now() - record.date
        #                         record.time_last = delta.days
        #                         # record.time_last_1 = delta.days


        available = fields.Float(readonly=True)

        def _view_internal_add(self):

                view_str = """
                WITH added_row_number AS (
                    select ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY date desc) AS row_number, *, (CURRENT_DATE::date - date::date) as time_last_1 from (
                    select sml.id as idd, sml.id, sml.date, sml.product_id, prt.categ_id , 
                    (
                    select sum(quantity) from stock_quant
                    INNER join stock_location l ON
    						stock_quant.location_id=l.id
    						and l.usage in ('internal','transit')
                    where product_id=sml.product_id and stock_quant.company_id = %s
                    ) as available,
                    sl.id as location_id, sml.reference as description,
                    case when sml.state ='done' then round(sml.qty_done, 4) else 0 end as qty_on_hand,
                    case when sml.state ='done' then round(sml.qty_done, 4) else 0 end as qty_add,
                    
                    0 as qty_ded,
    				sl.company_id,
                    spt.code as move_type

                    from stock_move_line sml 
                    INNER join stock_move sm ON sm.id = sml.move_id

                    left join stock_location sll ON
    						sml.location_id=sll.id
                    left join stock_location sl ON
                            sml.location_dest_id = sl.id
                    left join stock_picking sp ON
                            sp.id = sml.picking_id
                    left join stock_picking_type spt ON
                            sp.picking_type_id = spt.id
					
					left join product_product pr ON
                            sml.product_id = pr.id
					left join product_template prt ON
                            pr.product_tmpl_id = prt.id
						
                    where sl.usage='internal'
                    and sml.state != 'cancel'
                    and sml.company_id = sl.company_id
                    and sml.qty_done != 0
                    and sml.state ='done'
                    and pr.active = true
                    and sl.company_id = %s
                """ % (self.env.company.id,self.env.company.id)
                return view_str

        def _view_internal_deduct(self):
                view_str = """
                    select sml.id as idd, -sml.id, sml.date, sml.product_id, prt.categ_id, (
                    select sum(quantity) from stock_quant
                    INNER join stock_location l ON
    						stock_quant.location_id=l.id
    						and l.usage in ('internal','transit')
                    where product_id=sml.product_id and stock_quant.company_id = %s) as available,
                    sl.id as location_id, sml.reference as description,
                    case when sml.state ='done' then round(-sml.qty_done, 4) else 0 end as qty_on_hand,
                    0 as qty_add,
                    case when sml.state ='done' then round(sml.qty_done, 4) else 0 end as qty_ded,
                    sl.company_id,
                    spt.code as move_type

                    from stock_move_line sml 
                    INNER join stock_move sm ON sm.id = sml.move_id
                    left join stock_location sll ON
    						sml.location_dest_id=sll.id
                    left join stock_location sl ON
    						sml.location_id = sl.id
                    left join stock_picking sp ON
                            sp.id = sml.picking_id
                    left join stock_picking_type spt ON
                            sp.picking_type_id = spt.id

					left join product_product pr ON
                            sml.product_id = pr.id
					left join product_template prt ON
                            pr.product_tmpl_id = prt.id

						
                    where sl.usage='internal'
                    and sml.state != 'cancel'
                    and sml.company_id = sl.company_id
                    and sml.qty_done != 0
                    and sml.state ='done'
                    and pr.active = true
                    and sl.company_id = %s

                ) as fulll
                )
                SELECT
                *
                FROM added_row_number
                WHERE row_number = 1 and available>0

                """ % (self.env.company.id,self.env.company.id)
                return view_str


        @property
        def _table_query(self):
                return '%s union all %s' % (self._view_internal_add(), self._view_internal_deduct())
        
        # def init(self):
        #         # tools.drop_view_if_exists(self.env.cr, self._table)
        #         self.env.cr.execute('DROP TABLE if EXISTS %s' % self._table)
        #         self.env.cr.execute(sql.SQL("CREATE TABLE {} as ({} union all {})").format(sql.Identifier(self._table), sql.SQL(self._view_internal_add()), sql.SQL(self._view_internal_deduct())))


                # tools.drop_view_if_exists(self.env.cr, self._table)
                # sql ="""CREATE TABLE %s as (
                # %s
                # union all
                # %s
                # )""" % (self._table,
                #         self._view_internal_add(),
                #         self._view_internal_deduct()
                #         )
                # self.env.cr.execute(sql)

        @api.model
        def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):

                res = super(StockMoveTimeSimp, self).read_group(domain, fields, groupby, offset=offset, limit=limit, orderby=orderby, lazy=lazy)

                for line in res:
                        if '__domain' in line:
                                lines = self.search(line['__domain'])
                                total_qty = sum(lines.mapped('qty_add')) - sum(lines.mapped('qty_ded'))
                                total_qty_add = sum(lines.mapped('qty_add'))
                                total_qty_ded = sum(lines.mapped('qty_ded'))
                                line['qty_current'] = total_qty
                                line['qty_add'] = total_qty_add
                                line['qty_ded'] = total_qty_ded

                return res
