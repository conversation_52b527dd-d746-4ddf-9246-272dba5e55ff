<?xml version="1.0" encoding="UTF-8"?>
<odoo>

        <record id="stock_move_time_view_search" model="ir.ui.view">
            <field name="name">StockMoveTimeSearch</field>
            <field name="model">stock.move.time</field>
            <field name="arch" type="xml">
                <search string="Stock Moves Time">
                    <field name="product_id"/>
                    <field name="location_id" string="Location"/>
                    <field name="date"/>
                    <field name="company_id"/>
                    <!-- <field name="location_name_to" string="Location To"/> -->
                    <field name="location_name_from" string="Location name"/>
                    <filter string="Location" name="group_by_location" icon="terp-gtk-jump-to-rtl" domain="[]" context="{'group_by':'location_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Product" name="group_by_product" icon="terp-accessories-archiver" domain="[]"  context="{'group_by':'product_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Product Category" name="group_by_product_cat" icon="terp-accessories-archiver" domain="[]"  context="{'group_by':'categ_id'}"/>
                </search>
            </field>
        </record>


        <record id="filter_location_product" model="ir.filters">
            <field name="name">Location</field>
            <field name="model_id">stock.move.time</field>
            <field name="context">{'group_by': ['location_id', 'product_id']}</field>
        </record>

        <record id="filter_product_productlo" model="ir.filters">
            <field name="name">Product</field>
            <field name="model_id">stock.move.time</field>
            <field name="context">{'group_by': ['product_id', 'location_id']}</field>
        </record>

        <record id="stock_move_time_view_form" model="ir.ui.view" >
            <field name="name">StockMoveTimeViewForm</field>
            <field name="model">stock.move.time</field>
            <field name="arch" type="xml">
                <form string="Time Product Moves" create="0" edit="0">
                  <group>
                    <group>
                        <field name="date"/>
                        <field name="description"/>
                        <field name="location_id"/>
                        <field name="company_id"/>
                    </group>
                    <group>
                        <field name="product_id"/>
                        <!-- <field name="qty_add"/> -->
                        <!-- <field name="qty_ded"/> -->
                        <field name="qty_current"/>
                        <field name="time_last" invisible='1'/>
                        <field name="time_last_1"/>
                    </group>
                  </group>
                </form>
            </field>
        </record>

        <record id="stock_move_time_view_tree" model="ir.ui.view">
            <field name="name">StockMoveTimeViewTree</field>
            <field name="model">stock.move.time</field>
            <field name="arch" type="xml">
                <tree string="Time Product Moves" create="0" default_order="date">
                    <field name="date"/>
                    <field name="product_id"/>
                    <field name="categ_id"/>
                    <field name="location_id" invisible="1"/>
                    <field name="location_name_from"/>
                    <!-- <field name="location_name_to"/> -->
                    <field name="company_id" invisible='1'/>
                    <!-- <field name="qty_add" sum="Qty Add"/> -->
                    <!-- <field name="qty_ded" sum="Qty Ded"/> -->
                    <field name="qty_current" sum="Qty Curr"/>
                    <field name="time_last" invisible='1'/>
                    <field name="time_last_1" sum="Days"/>
                </tree>
            </field>
        </record>


        <record id="stock_move_time_action" model="ir.actions.act_window" >
            <field name="name">Stock Location Life cycle</field>
            <field name="res_model">stock.move.time</field>
            <field name="view_mode">tree,graph,form</field>
            <field name="context">{'group_by':['product_id', 'location_id']}</field>
            <field name="view_id" ref="stock_move_time_view_tree"/>
            <field name="search_view_id" ref="stock_move_time_view_search"/>
        </record>
        <record model="ir.ui.view" id="stock_move_time_graph">
           <field name="name">Stock Location Life cycle</field>
           <field name="model">stock.move.time</field>
           <field name="type">graph</field>
           <field name="arch" type="xml">
                 <graph string="Stock Location Life cycle">
                      <field name="categ_id" type="measure"/>
                      <field name="time_last" invisible='1'/>
                      <field name="time_last_1" operator="*"/>
                </graph>
            </field>
        </record>

        <act_window
                context="{'group_by':['location_id']}"
                domain="[('product_id', '=', active_id)]"
                id="stock_move_time_action_product_moves"
                name="Location Moves"
                res_model="stock.move.time"
                binding_model="product.product"
        />

        <act_window
                context="{'group_by':['location_id']}"
                domain="[('product_tmpl_id', 'in', [active_id])]"
                id="stock_move_time_action_product_tmpl_moves"
                name="Location Moves"
                res_model="stock.move.time"
                binding_model="product.template"
        />
        <!-- <menuitem id="menu_action_stock_move_time_report" name="Stock Location Life cycle" action="stock_move_time_action" sequence="160" parent="stock.menu_warehouse_report"/> -->




        <!-- Simp View -->
        <record id="stock_move_time_simp_view_search" model="ir.ui.view">
            <field name="name">StockMoveTimeSimpSearch</field>
            <field name="model">stock.move.time.simp</field>
            <field name="arch" type="xml">
                <search string="Stock Moves Time Simp">
                    <field name="product_id"/>
                    <field name="location_id" string="Location"/>
                    <field name="date"/>
                    <field name="company_id"/>
                    <field name="location_name_from" string="Location name"/>
                    <filter string="Location" name="group_by_location" icon="terp-gtk-jump-to-rtl" domain="[]" context="{'group_by':'location_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Product" name="group_by_product" icon="terp-accessories-archiver" domain="[]"  context="{'group_by':'product_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Product Category" name="group_by_product_cat" icon="terp-accessories-archiver" domain="[]"  context="{'group_by':'categ_id'}"/>
                </search>
            </field>
        </record>

        <record id="stock_move_time_simp_view_tree" model="ir.ui.view">
            <field name="name">StockMoveTimeSimpViewTree</field>
            <field name="model">stock.move.time.simp</field>
            <field name="arch" type="xml">
                <tree string="Time Product Moves Simp" create="0" default_order="date">
                    <field name="date"/>
                    <field name="product_id"/>
                    <field name="categ_id"/>
                    <field name="location_id" invisible="1"/>
                    <field name="location_name_from" invisible="1"/>
                    <field name="company_id" invisible='1'/>
                    <!-- <field name="time_last" invisible='1'/> -->
                    <field name="time_last_1" sum="Days"/>
                    <field name="available"/>
                </tree>
            </field>
        </record>

        <record id="stock_move_time_simp_action" model="ir.actions.act_window" >
            <field name="name">Stock Product Life cycle</field>
            <field name="res_model">stock.move.time.simp</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="context"></field>
            <field name="view_id" ref="stock_move_time_simp_view_tree"/>
            <field name="search_view_id" ref="stock_move_time_simp_view_search"/>
        </record>
        <record model="ir.ui.view" id="stock_move_time_simp_graph">
           <field name="name">Stock Product Life cycle</field>
           <field name="model">stock.move.time.simp</field>
           <field name="type">graph</field>
           <field name="arch" type="xml">
                 <graph string="Stock Product Life cycle">
                      <field name="categ_id" group="True"/>
                      <field name="time_last_1" operator="*"/>
                </graph>
            </field>
        </record>

        <record id="stock_move_time_simp_pivot" model="ir.ui.view">
            <field name="name">Stock Product Life cycle pivot</field>
            <field name="model">stock.move.time.simp</field>
            <field name="arch" type="xml">
                <pivot string="Stock Product Life cycle">
                    <field name="categ_id"  type="row"/>
                </pivot>
            </field>
        </record>
   

        <menuitem id="menu_action_stock_move_time_simp_report" name="Stock Product Life cycle" action="stock_move_time_simp_action" sequence="160" parent="stock.menu_warehouse_report"/>
</odoo>
