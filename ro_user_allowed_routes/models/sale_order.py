# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID

from odoo.osv import expression
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.tools.float_utils import float_compare, float_is_zero, float_round
from odoo.exceptions import UserError
from collections import defaultdict



# class SaleOrder(models.Model):
#     _inherit = 'sale.order'


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'


    # route_id = fields.Many2one('stock.route', string='Route', domain=[('sale_selectable', '=', True)], ondelete='restrict', check_company=True)

    allowed_routes_ids = fields.Many2many('stock.route',compute="get_allowed_routes")
    @api.depends('product_id')
    def get_allowed_routes(self):
        for rec in self:
            rec.allowed_routes_ids = False

            routes = self.env['stock.route'].search([('sale_selectable','=',True)])
            own_routes = routes.filtered(lambda x:self.env.user in x.route_user_ids)

            if len(own_routes)>0:
                rec.allowed_routes_ids = own_routes


    @api.onchange('product_id')
    def _onchange_line_get_route(self):
        for rec in self:
            rec.get_allowed_routes()
            if rec.allowed_routes_ids:
                rec.route_id = rec.allowed_routes_ids[0]