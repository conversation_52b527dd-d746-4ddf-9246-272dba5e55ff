# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* internal_transfer_dual_validate_location
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-19 08:55+0000\n"
"PO-Revision-Date: 2022-12-19 08:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: internal_transfer_dual_validate_location
#: model:ir.model.fields,help:internal_transfer_dual_validate_location.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
" * مسودة: الشحنة غير مؤكدة بعد. الحجز غير منطبق.\n"
" * بانتظار عملية أخرى: هذه العملية بانتظار عملية أخرى قبل أن تصبح جاهزة.\n"
" * قيد الانتظار: الشحنة بانتظار توافر بعض المنتجات.\n"
"(أ) سياسة الشحن هي \"في أسرع وقت ممكن\": لم نتمكن من حجز أي منتج.\n"
"(ب) سياسة الشحن هي \"عندما تكون كافة المنتجات جاهزة\": لا يمكن حجز كافة المنتجات.\n"
" * جاهزة: الشحنة جاهزة لمعالجتها.\n"
"(a) سياسة الشحن هي في أسرع ممكن\": تم حجز منتج واحد على الأقل.\n"
"(b) سياسة الشحن هي \"عندما تكون كافة المنتجات جاهزة\": لقد تم حجز كافة المنتجات.\n"
" * منتهية: لقد تمت معالجة الشحنة.\n"
" * ملغية: لقد تم إلغاء الشحنة."

#. module: internal_transfer_dual_validate_location
#: model_terms:ir.ui.view,arch_db:internal_transfer_dual_validate_location.stock_picking_form_inherit
msgid "<span class=\"o_stat_text\">Detailed Operations</span>"
msgstr ""

#. module: internal_transfer_dual_validate_location
#: model:ir.model.fields,field_description:internal_transfer_dual_validate_location.field_stock_picking__flag
msgid "Flag"
msgstr ""

#. module: internal_transfer_dual_validate_location
#: model:ir.model,name:internal_transfer_dual_validate_location.model_stock_location
msgid "Inventory Locations"
msgstr "مواقع المخزون"

#. module: internal_transfer_dual_validate_location
#: model_terms:ir.ui.view,arch_db:internal_transfer_dual_validate_location.stock_picking_form_inherit
msgid "List view of operations"
msgstr ""

#. module: internal_transfer_dual_validate_location
#: model:ir.model.fields,field_description:internal_transfer_dual_validate_location.field_stock_location__location_user_ids
msgid "Location User"
msgstr ""

#. module: internal_transfer_dual_validate_location
#: model:ir.model.fields.selection,name:internal_transfer_dual_validate_location.selection__stock_picking__state__validate2
#: model_terms:ir.ui.view,arch_db:internal_transfer_dual_validate_location.stock_picking_form_inherit
#: model_terms:ir.ui.view,arch_db:internal_transfer_dual_validate_location.stock_picking_search_validate2
msgid "Second Validate"
msgstr "الاعتماد الثاني"

#. module: internal_transfer_dual_validate_location
#: model:ir.model.fields,field_description:internal_transfer_dual_validate_location.field_stock_picking__state
msgid "Status"
msgstr "الحالة"

#. module: internal_transfer_dual_validate_location
#: model_terms:ir.ui.view,arch_db:internal_transfer_dual_validate_location.view_picking_move_line_tree
msgid "Stock Move Line"
msgstr ""

#. module: internal_transfer_dual_validate_location
#: code:addons/internal_transfer_dual_validate_location/models/models.py:0
#, python-format
msgid "Stock Move Lines"
msgstr ""

#. module: internal_transfer_dual_validate_location
#: model:ir.model,name:internal_transfer_dual_validate_location.model_stock_picking
#: model_terms:ir.ui.view,arch_db:internal_transfer_dual_validate_location.stock_move_line_search_validate2
msgid "Transfer"
msgstr "الشحنة"

#. module: internal_transfer_dual_validate_location
#: model_terms:ir.ui.view,arch_db:internal_transfer_dual_validate_location.view_picking_move_line_tree
msgid "Unit of Measure"
msgstr ""

#. module: internal_transfer_dual_validate_location
#: code:addons/internal_transfer_dual_validate_location/models/models.py:0
#: code:addons/internal_transfer_dual_validate_location/models/models.py:0
#, python-format
msgid "You cannot validate. Please wait for the other employee to approve ."
msgstr ""
