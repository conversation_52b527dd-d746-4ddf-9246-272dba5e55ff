<odoo>
    <data>
        <record model="ir.ui.view" id="sale_order_form_route_inherit">
            <field name="name">sale order form view inherit</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_stock.view_order_form_inherit_sale_stock"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='order_line']/tree/field[@name='route_id']" position="after">
                    <field name="allowed_routes_ids" widget="many2many_tags" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='order_line']/tree/field[@name='route_id']" position="attributes">
                    <attribute name="domain">[('id','in',allowed_routes_ids)]</attribute>
                </xpath>

                <xpath expr="//field[@name='order_line']/form//field[@name='route_id']" position="after">
                    <field name="allowed_routes_ids" widget="many2many_tags" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='order_line']/form//field[@name='route_id']" position="attributes">
                    <attribute name="domain">[('id','in',allowed_routes_ids)]</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
