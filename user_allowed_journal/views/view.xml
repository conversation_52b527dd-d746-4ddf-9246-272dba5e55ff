<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>

        <!-- <record id="account_move_journal_inherit" model="ir.ui.view">
            <field name="name">account_move_journal domain</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="before">
                    <field name="allowed_journal_ids" string="Allowed Journals" invisible='1'/>
                </xpath>
                <xpath expr="//field[@name='journal_id']" position="attributes">
                    <attribute name="domain">[('id','in',allowed_journal_ids)]</attribute>
                </xpath>
            </field>
        </record> -->

        <record id="res_users_allowed_journal_inherit" model="ir.ui.view">
            <field name="name">res users allowed journal</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='access_rights']/group" position="after">
                    <group>
                        <group>
                            <field name="journal_ids" string="Allowed Journals" widget="many2many_tags"/>
                            <field name="cash_in_out_account_ids" string="Cash in/out Accounts" widget="many2many_tags"/>
                        </group>
                    </group>
                </xpath>
            </field>
        </record>
    
    </data>

</odoo>
