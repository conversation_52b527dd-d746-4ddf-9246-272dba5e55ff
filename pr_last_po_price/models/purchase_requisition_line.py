# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class PurchaseRequisitionLine(models.Model):
    _inherit = 'purchase.requisition.line'
                    
    @api.depends('product_id')
    def _compute_last_price(self):
        for rec in self:
            rec.last_po_price = 0

            if rec.product_id:
                po_objs = self.env['purchase.order.line'].search([('product_id', '=', rec.product_id.id), 
                                                                  ('order_id.state', '=', ('purchase', 'done'))
                                                                  ])
                if len(po_objs)>0:
                    rec.last_po_price =  po_objs[0].price_unit

    last_po_price = fields.Float(compute="_compute_last_price")

