# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


# class ProductProduct(models.Model):
#     _inherit = 'product.product'

    # def _compute_locations_qty(self):
    #     for product in self:
    #         product.locations_qty = ""

    #         if product:
    #             quant_objs = self.env['stock.quant'].search([('product_id', '=', product.id), ('location_id.usage', '=', 'internal')])  
                
    #             for location in quant_objs:                    
    #                 product.locations_qty +=  location.location_id.display_name + ": "+str(location.available_quantity)+"\n"
                    
                    
    # locations_qty = fields.Text(compute="_compute_locations_qty")


