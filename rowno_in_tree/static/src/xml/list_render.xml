<?xml version="1.0" encoding="UTF-8" ?>
<template>
    <t
        t-name="rowno_in_tree.ListRenderer"
        t-inherit="web.ListRenderer"
        t-inherit-mode="extension"
        owl="1"

    >
        <xpath expr="//table/thead/tr/th[@t-if='hasSelectors']" position="before">
            <th>#</th>
        </xpath>
        <xpath expr="//div/table/tfoot/tr/td" position="before">
            <td />
        </xpath>
    </t>

    <t
        t-name="rowno_in_tree.add_number"
        t-inherit="web.ListRenderer.Rows"
        t-inherit-mode="extension"
        owl="1"
    >
        <xpath expr="//t[@t-foreach='list.records']" position="before">
            <t t-set="RowNumber" t-value="1" />
        </xpath>
        <xpath
            expr="//t[@t-call='{{ constructor.recordRowTemplate }}']"
            position="after"
        >
            <t t-set="RowNumber" t-value="RowNumber+1" />
        </xpath>
    </t>

    <t
        t-name="rowno_in_tree.ListRenderer.RecordRowNumber"
        t-inherit="web.ListRenderer.RecordRow"
        t-inherit-mode="extension"
        owl="1"
    >
        <xpath expr="//td[@class='o_list_record_selector']" position="before">
            <td tabindex="-1" style="overflow:unset !important;" t-if='RowNumber'>
                <span t-esc="RowNumber" />
            </td>
        </xpath>
    </t>

    <t
        t-name="rowno_in_tree.GroupRow"
        t-inherit="web.ListRenderer.GroupRow"
        t-inherit-mode="extension"
        owl="1"

    >
        <xpath expr="//tr/th[1]" position="attributes">
            <attribute name="t-att-colspan">getGroupNameCellColSpan(group)+1</attribute>
        </xpath>
    </t>
</template>
