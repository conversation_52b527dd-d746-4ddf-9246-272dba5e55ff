<?xml version='1.0' encoding='utf-8'?>
<odoo>
  <data>
    <record id="landscape_paperformat_partner_target" model="report.paperformat">
      <field name="name">LandScape layout Partner Target</field>
      <field name="default" eval="True" />
      <field name="format">A4</field>
      <field name="page_height">0</field>
      <field name="page_width">0</field>
      <field name="orientation">Landscape</field>
      <field name="margin_top">25</field>
      <field name="margin_bottom">15</field>
      <field name="margin_left">7</field>
      <field name="margin_right">7</field>
      <field name="header_line" eval="False" />
      <field name="header_spacing">20</field>
      <field name="dpi">90</field>
    </record>
  
     <record id="action_partner_target_report" model="ir.actions.report">
            <field name="name">Partner Target Report</field>
            <field name="model">res.partner</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">partner_target_report.partner_target_report_document</field>
            <field name="report_file">partner_target_report.partner_target_report_document</field>
            <field name="paperformat_id" ref="partner_target_report.landscape_paperformat_partner_target"/>
            <field name="binding_model_id" eval="False" />
            <field name="binding_type">report</field>
      </record>

      <template id="custom_external_layout_footer_partner_target_report">
            <div class="footer">
            <div class="row" style="direction:rtl">
              <div class="col-4">
                <p style = "float:right; ">
                    اسم المستخدم:
                  <span t-esc="data['printed_user'].name"/>
                </p>
              </div>
              <div class="col-4"/>
              <div class="col-4">
                <p style = "float:left; ">
                  تاريخ الطباعة:
                  <span t-esc="data['date']"/>
                </p>

              </div>
             
            </div>

          </div>
      </template>

      <template id="custom_external_layout_partner_target_report">

            <t t-if="not company">
                <t t-if="company_id">
                    <t t-set="company" t-value="company_id"/>
                </t>
                <t t-elif="o and 'company_id' in o">
                    <t t-set="company" t-value="o.company_id.sudo()"/>
                </t>
                <t t-else="else">
                    <t t-set="company" t-value="res_company"/>
                </t>
            </t>

            <div t-attf-class="header o_company_#{data['company'].id}_layout" t-att-style="report_header_style">
              <div class="row">
                  <div class="col-3 mb4">
                      <img t-if="data['company'].logo" t-att-src="image_data_uri(data['company'].logo)" style="max-height: 50px;" alt="Logo"/>
                  </div>
                  <div class="col-9 text-right" style="margin-top:22px;" t-field="company.report_header" name="moto"/>
              </div>
              
            </div>

            <div t-attf-class="o_company_#{company.id}_layout article o_report_layout_background" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
                <t t-call="web.address_layout"/>
                <t t-raw="0"/>
                
            </div>
            <t t-call="partner_target_report.custom_external_layout_footer_partner_target_report"/>
        </template>

        <template id="partner_target_report_document">
            <t t-call="web.html_container">
                <t t-call="partner_target_report.custom_external_layout_partner_target_report">
                   
                  <div class="page">
                    <div class="row" style="direction:rtl">
                      <div class="col-3"/>
                      <div class="col-3">
                        <p style = "float:right; ">
                            من:
                            <span t-esc="data['from']"/>
                        </p>
                      </div>
        
                      <div class="col-3">
                        <p style = "float:left; ">
                          إلى:
                          <span t-esc="data['to']"/>
                        </p>
                      </div>
                      <div class="col-3"/>
                    </div>

                    <div class="row">
                      <div style="width:100%">
                        <center>
                          <h3 style="text-align:center">Partner Sale Target</h3>
                        </center>
                      </div>
                    </div>

                    <br/>

                    <table class="table table-bordered " style="direction:rtl">
                      <tr >
                        <th class="text-center">قسم/الأحياء</th>
                        <th class="text-center">المدينه</th>
                        <th class="text-center">المحافظه</th>
                        <th class="text-center">خط السير</th>
                        <th class="text-center">تصنيف العملاء</th>
                        <th class="text-center">اسماء العملاء</th>
                        <th class="text-center">خطة العميل قيمه</th>
                        <th class="text-center">إجمالي المبيعات بالقيمة</th>
                        <th class="text-center">نسبة التحققيق(%)</th>
                        <th class="text-center">عدد الأصناف</th>
                        <th class="text-center">إجمالي التالف بالقيمة</th>
                        <th class="text-center">نسبة التالف(%)</th>
                        <th class="text-center">حدود الإئتمان</th>
                        <th class="text-center">قيمة المديونيه</th>
                        <th class="text-center">التجاوز</th>
                      </tr>
                      
                      <t t-foreach="data['lines']" t-as="line">
                        <tr>
                          <td><span t-esc="line['address']"/></td>
                          <td><span t-esc="line['city']"/></td>
                          <td><span t-esc="line['state']"/></td>
                          <td><span t-esc="line['team']"/></td>
                          <td><span t-esc="line['class']"/></td>
                          <td><span t-esc="line['name']"/></td>
                          <td><span t-esc="line['target']"/></td>
                          <td><span t-esc="line['total_sales']"/></td>
                          <td><span t-esc="line['achieve_perc']"/></td>
                          <td><span t-esc="line['product_count']"/></td>
                          <td><span t-esc="line['refund_amount']"/></td>
                          <td><span t-esc="line['refund_percent']"/></td>
                          <td><span t-esc="line['credit_limit']"/></td>
                          <td><span t-esc="line['total_due']"/></td>
                          <td><span t-esc="line['overtaking']"/></td>
                        </tr>
                      </t>

                    </table>


                  </div>
                </t>
            </t>
        </template>  
  </data>
  
</odoo>