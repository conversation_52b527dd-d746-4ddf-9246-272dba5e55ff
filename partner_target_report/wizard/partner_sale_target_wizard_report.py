from odoo import models, fields, _
import base64
from datetime import date
from . import prepare_sale_target_xlsx


# a Transient model to return the report
class WizardPartnerSaleReport(models.TransientModel):
    _name = 'wizard.partner.sale.report.pdf'
    _description = 'wizard Partner Sale Target PDF report'

    file_name = fields.Char('File Name', size=64)
    report_file = fields.Binary('Prepared File', readonly=True)


# a Transient model to get data parameters
class PartnerSaleTargetWizard(models.TransientModel):
    _name = 'partner.sale.target.wizard.report'

    # region Fields
    partner_ids = fields.Many2many('res.partner', domain=[('parent_id', '=', False)])

    date_from = fields.Date(default=fields.Date.context_today, required=True)
    date_to = fields.Date(default=fields.Date.context_today, required=True)

    team_ids = fields.Many2many('crm.team')
    # endregion

    # region Helper functions
    def _get_partner_target_data(self):

        partners = self.env['res.partner']
        move = self.env['account.move']

        if len(self.partner_ids) > 0:
            partners = self.partner_ids
        else:
            all_partners = partners.search([])
            partners = all_partners.filtered(lambda x:not x.parent_id)

        if len(self.team_ids) > 0:
            partners = all_partners.filtered(lambda x: x.team_id in self.team_ids)

        vals = []

        for partner in partners:
            partner_child = partner.ids + partner.child_ids.ids
            inv_domain = [('state', '=', 'posted'), ('partner_id', 'in', partner_child),
                          ('invoice_date', '>=', self.date_from), ('invoice_date', '<=', self.date_to)]
            out_domain = inv_domain + [('move_type', '=', 'out_invoice')]
            refund_domain = inv_domain + [('move_type', '=', 'out_refund')]

            out_invoices = move.search(out_domain)
            out_refund = move.search(refund_domain)

            total_achieved = sum(out_invoices.mapped('amount_total')) - sum(out_refund.mapped('amount_total'))

            lines = out_invoices.line_ids | out_refund.line_ids

            vals.append({
                'address': partner.street,
                'city': partner.city,
                'state': partner.state_id.name,
                'team': partner.team_id.name,
                'class': partner.class_id.name,
                'name': partner.name,
                'target': partner.target,
                'total_sales': round(total_achieved, 2),
                'achieve_perc': str(
                    round((total_achieved / partner.target) * 100, 2) if partner.target > 0 else 0) + "%",
                'product_count': len(lines.mapped('product_id')),
                'refund_amount': round(sum(out_refund.mapped('amount_total')), 2),
                'refund_percent': str(round((sum(out_refund.mapped('amount_total')) / total_achieved) * 100,
                                            2) if total_achieved > 0 else 0) + "%",
                'credit_limit': partner.credit_limit,
                'total_due': partner.total_due,
                'overtaking': round(
                    partner.total_due - partner.credit_limit if partner.total_due > partner.credit_limit else 0, 2)
            })

        data = {'lines': vals, 'date': date.today(), 'printed_user': self.env.user, 'company': self.env.company,
                'from': self.date_from, 'to': self.date_to}
        # print(data)

        data = {
            'doc_ids': partners.ids,
            'doc_model': 'res.partner',
            'data': data,
        }
        return data

    def _return_file_notification(self, file_name, file):
        attachment_id = self.env['wizard.partner.sale.report.pdf'].create({
            'file_name': file_name,
            'report_file': base64.b64encode(file),
        })
        return {
            'name': _('Notification'),
            'context': self.env.context,
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'wizard.partner.sale.report.pdf',
            'res_id': attachment_id.id,
            'data': None,
            'type': 'ir.actions.act_window',
            'target': 'new'
        }
    # endregion

    # region Action Functions
    def get_partner_target_report_pdf(self):
        data = self._get_partner_target_data()
        file_name = 'Partner Target.pdf'
        pdf = self.env['ir.actions.report']._render_qweb_pdf('partner_target_report.action_partner_target_report',
                                                             data=data)
        return self._return_file_notification(file_name, pdf[0])

    def get_partner_target_report_excel(self):
        data = self._get_partner_target_data()
        report_name = "Partner Sale Target"
        file_name = f'{report_name}.xlsx'
        xlsx = prepare_sale_target_xlsx.get_excel(report_name, data['data'])
        return self._return_file_notification(file_name, xlsx)







    # endregion
