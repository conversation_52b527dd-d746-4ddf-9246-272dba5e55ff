from io import BytesIO
import xlsxwriter
from datetime import datetime

    # columnsEN = [
    #     ('قسم/الأحياء', 20, 'char', 'char'),
    #
    #     ('المدينه', 15, 'char', 'char'),
    #
    #     ('المحافظه', 15, 'char', 'char'),
    #
    #     ('خط السير', 15, 'char', 'char'),
    #
    #     # ('تصنيف العملاء', 10, 'char', 'char'),
    #
    #     ('اسماء العملاء', 15, 'char', 'char'),
    #
    #     ('خطة العميل قيمه', 15, 'char', 'char'),
    #
    #     ('إجمالي المبيعات بالقيمة', 15, 'char', 'char'),
    #
    #     ('نسبة التحققيق(%)', 15, 'char', 'char'),
    #
    #     ('عدد الأصناف', 15, 'char', 'char'),
    #
    #     ('إجمالي التالف بالقيمة', 15, 'char', 'char'),
    #
    #     ('نسبة التالف(%)', 15, 'char', 'char'),
    #
    #     ('حدود الإئتمان', 15, 'char', 'char'),
    #
    #     ('قيمة المديونيه', 15, 'char', 'char'),
    #
    #     (('التجاوز')_, 15, 'char', 'char')
    # ]
def add_workbook_format(workbook):
    colors = {
        'white_orange': '#FFFFDB',
        'orange': '#FFC300',
        'red': '#FF0000',
        'yellow': '#F6FA03',
    }

    wbf = {}
    wbf['header'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': '#FFFFDB', 'font_color': '#000000', 'font_name': 'Georgia'})
    wbf['header'].set_border()

    wbf['header_orange'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': colors['orange'], 'font_color': '#000000',
         'font_name': 'Georgia'})
    wbf['header_orange'].set_border()

    wbf['header_yellow'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': colors['yellow'], 'font_color': '#000000',
         'font_name': 'Georgia'})
    wbf['header_yellow'].set_border()

    wbf['header_no'] = workbook.add_format(
        {'bold': 1, 'align': 'center', 'bg_color': '#FFFFDB', 'font_color': '#000000', 'font_name': 'Georgia'})
    wbf['header_no'].set_border()
    wbf['header_no'].set_align('vcenter')

    wbf['footer'] = workbook.add_format({'align': 'left', 'font_name': 'Georgia'})

    wbf['content_datetime'] = workbook.add_format({'num_format': 'yyyy-mm-dd hh:mm:ss', 'font_name': 'Georgia'})
    wbf['content_datetime'].set_left()
    wbf['content_datetime'].set_right()

    wbf['content_date'] = workbook.add_format({'num_format': 'yyyy-mm-dd', 'font_name': 'Georgia'})
    wbf['content_date'].set_left()
    wbf['content_date'].set_right()

    wbf['title_doc'] = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 20,
        'font_name': 'Georgia',
    })

    wbf['company'] = workbook.add_format({'align': 'left', 'font_name': 'Georgia'})
    wbf['company'].set_font_size(11)

    wbf['content'] = workbook.add_format()
    wbf['content'].set_left()
    wbf['content'].set_right()

    wbf['content_float'] = workbook.add_format({'align': 'right', 'num_format': '#,##0.00', 'font_name': 'Georgia'})
    wbf['content_float'].set_right()
    wbf['content_float'].set_left()

    wbf['content_number'] = workbook.add_format({'align': 'right', 'num_format': '#,##0', 'font_name': 'Georgia'})
    wbf['content_number'].set_right()
    wbf['content_number'].set_left()

    wbf['content_percent'] = workbook.add_format({'align': 'right', 'num_format': '0.00%', 'font_name': 'Georgia'})
    wbf['content_percent'].set_right()
    wbf['content_percent'].set_left()

    wbf['total_float'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['white_orange'], 'align': 'right', 'num_format': '#,##0.00',
         'font_name': 'Georgia'})
    wbf['total_float'].set_top()
    wbf['total_float'].set_bottom()
    wbf['total_float'].set_left()
    wbf['total_float'].set_right()

    wbf['total_number'] = workbook.add_format(
        {'align': 'right', 'bg_color': colors['white_orange'], 'bold': 1, 'num_format': '#,##0',
         'font_name': 'Georgia'})
    wbf['total_number'].set_top()
    wbf['total_number'].set_bottom()
    wbf['total_number'].set_left()
    wbf['total_number'].set_right()

    wbf['total'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['white_orange'], 'align': 'center', 'font_name': 'Georgia'})
    wbf['total'].set_left()
    wbf['total'].set_right()
    wbf['total'].set_top()
    wbf['total'].set_bottom()

    wbf['total_float_yellow'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['yellow'], 'align': 'right', 'num_format': '#,##0.00',
         'font_name': 'Georgia'})
    wbf['total_float_yellow'].set_top()
    wbf['total_float_yellow'].set_bottom()
    wbf['total_float_yellow'].set_left()
    wbf['total_float_yellow'].set_right()

    wbf['total_number_yellow'] = workbook.add_format(
        {'align': 'right', 'bg_color': colors['yellow'], 'bold': 1, 'num_format': '#,##0', 'font_name': 'Georgia'})
    wbf['total_number_yellow'].set_top()
    wbf['total_number_yellow'].set_bottom()
    wbf['total_number_yellow'].set_left()
    wbf['total_number_yellow'].set_right()

    wbf['total_yellow'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['yellow'], 'align': 'center', 'font_name': 'Georgia'})
    wbf['total_yellow'].set_left()
    wbf['total_yellow'].set_right()
    wbf['total_yellow'].set_top()
    wbf['total_yellow'].set_bottom()

    wbf['total_float_orange'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['orange'], 'align': 'right', 'num_format': '#,##0.00',
         'font_name': 'Georgia'})
    wbf['total_float_orange'].set_top()
    wbf['total_float_orange'].set_bottom()
    wbf['total_float_orange'].set_left()
    wbf['total_float_orange'].set_right()

    wbf['total_number_orange'] = workbook.add_format(
        {'align': 'right', 'bg_color': colors['orange'], 'bold': 1, 'num_format': '#,##0', 'font_name': 'Georgia'})
    wbf['total_number_orange'].set_top()
    wbf['total_number_orange'].set_bottom()
    wbf['total_number_orange'].set_left()
    wbf['total_number_orange'].set_right()

    wbf['total_orange'] = workbook.add_format(
        {'bold': 1, 'bg_color': colors['orange'], 'align': 'center', 'font_name': 'Georgia'})
    wbf['total_orange'].set_left()
    wbf['total_orange'].set_right()
    wbf['total_orange'].set_top()
    wbf['total_orange'].set_bottom()

    wbf['header_detail_space'] = workbook.add_format({'font_name': 'Georgia'})
    wbf['header_detail_space'].set_left()
    wbf['header_detail_space'].set_right()
    wbf['header_detail_space'].set_top()
    wbf['header_detail_space'].set_bottom()

    wbf['header_detail'] = workbook.add_format({'bg_color': '#E0FFC2', 'font_name': 'Georgia'})
    wbf['header_detail'].set_left()
    wbf['header_detail'].set_right()
    wbf['header_detail'].set_top()
    wbf['header_detail'].set_bottom()

    return wbf


def get_excel(report_name, data):
    fp = BytesIO()
    workbook = xlsxwriter.Workbook(fp)
    wbf = add_workbook_format(workbook)
    worksheet = workbook.add_worksheet(report_name)

# region sheet header
    row = 2
    worksheet.merge_range('A' + str(row) + ':F' + str(row + 1), report_name, wbf['title_doc'])
    worksheet.write(row, 8, 'تاريخ الطباعه', wbf['header_orange'])
    worksheet.write(row, 7, str(data['date'].strftime('%Y-%m-%d')), wbf['content'])
    worksheet.write(row-1, 8, 'اسم المستخدم', wbf['header_orange'])
    worksheet.write(row-1, 7, data['printed_user'].name, wbf['content'])
    worksheet.write(row, 11, 'تاريخ البداية', wbf['header_orange'])
    worksheet.write(row, 10, str(data['from'].strftime('%Y-%m-%d')), wbf['content'])
    worksheet.write(row - 1, 11, 'تاريخ النهاية', wbf['header_orange'])
    worksheet.write(row - 1, 10, str(data['to'].strftime('%Y-%m-%d')), wbf['content'])
# endregion

    columns = [
        ('قسم/الأحياء', 20, 'char', 'char'),

        ('المدينه', 15, 'char', 'char'),

        ('المحافظه', 15, 'char', 'char'),

        ('خط السير', 15, 'char', 'char'),

        ('تصنيف العملاء', 10, 'char', 'char'),

        ('اسماء العملاء', 15, 'char', 'char'),

        ('خطة العميل قيمه', 15, 'char', 'char'),

        ('إجمالي المبيعات بالقيمة', 15, 'char', 'char'),

        ('نسبة التحققيق(%)', 15, 'char', 'char'),

        ('عدد الأصناف', 15, 'char', 'char'),

        ('إجمالي التالف بالقيمة', 15, 'char', 'char'),

        ('نسبة التالف(%)', 15, 'char', 'char'),

        ('حدود الإئتمان', 15, 'char', 'char'),

        ('قيمة المديونيه', 15, 'char', 'char'),

        ('التجاوز', 15, 'char', 'char')
    ]
    row += 3
    col = 0
    for column in columns:
        column_name = column[0]
        column_width = column[1]
        worksheet.set_column(col, col, column_width)
        worksheet.write(row, col, column_name, wbf['header_orange'])
        col += 1

    for line in data['lines']:
        row += 1
        col = 0
        for key in line.keys():
            worksheet.write(row, col, line[key], wbf['content'])
            col += 1

    workbook.close()
    return fp.getvalue()
