from odoo import models, fields, api

class AccountPayment(models.Model):
    _inherit = 'account.payment'
    
    is_bookkeeper_user = fields.Boolean(compute="get_user_check_user_access")

    def get_user_check_user_access(self):
        for rec in self:
            rec.is_bookkeeper_user = False
            if self.env.user.has_group('account.group_account_user'):
                rec.is_bookkeeper_user = True

    @api.onchange('partner_id')
    def _onchange_partner_id_check_user_access(self):
        self.is_bookkeeper_user = False
        if self.env.user.has_group('account.group_account_user'):
            self.is_bookkeeper_user = True
