<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_payment_user_check_form_inherit" model="ir.ui.view">
        <field name="name">account.payment.form.inherit</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='is_internal_transfer']" position="after">
                <field name="is_bookkeeper_user" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="view_account_payment_cash_in_check_form_inherit" model="ir.ui.view">
        <field name="name">account.payment.form.inherit</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="send_receive_request.account_payment_document_in_inherited_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='destination_account_id']" position="attributes">
                <attribute name="attrs">{'required': [('post_type', '=', 'single')], 'invisible': [('post_type','!=','single')], 'readonly': ['|', '|', ('is_bookkeeper_user', '=', False), ('state', '!=', 'draft'), ('is_internal_transfer', '=', True)]}</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_account_payment_cash_out_check_form_inherit" model="ir.ui.view">
        <field name="name">account.payment.form.inherit</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="send_receive_request.account_payment_document_out_inherited_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='destination_account_id']" position="attributes">
                <attribute name="attrs">{'required': [('post_type', '=', 'single')], 'invisible': [('post_type','!=','single')], 'readonly': ['|', '|', ('is_bookkeeper_user', '=', False), ('state', '!=', 'draft'), ('is_internal_transfer', '=', True)]}</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_account_payment_cash_internal_check_form_inherit" model="ir.ui.view">
        <field name="name">account.payment.form.inherit</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="send_receive_request.account_payment_document_internal_inherited_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='destination_account_id']" position="attributes">
                <attribute name="attrs">{'readonly': ['|', '|', ('is_bookkeeper_user', '=', False), ('state', '!=', 'draft'), ('is_internal_transfer', '=', True)]}</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
        </field>
    </record>
    
</odoo>
