from odoo import fields, models, api


class SaleReport(models.Model):
    _inherit = "sale.report"

    parent_id = fields.Many2one('res.partner', string='Parent Partner', readonly=True)
    partner_ref = fields.Char(string='Customer Code', readonly=True)
    class_id = fields.Many2one(string='Class', comodel_name='partner.class', readonly=True)
    state_id = fields.Many2one(string='State', comodel_name='res.country.state', readonly=True)
    city = fields.Char(readonly=True)
    payment_term_id = fields.Many2one(string='Payment Terms', comodel_name='account.payment.term', readonly=True)



    def _select_additional_fields(self):
        res = super()._select_additional_fields()
        res['parent_id'] = "partner.parent_id"
        res['partner_ref'] = "partner.ref"
        res['class_id'] = "partner.class_id"
        res['state_id'] = "partner.state_id"
        res['city'] = "partner.city"
        res['payment_term_id'] = "s.payment_term_id"
        return res

    def _group_by_sale(self):
        res = super()._group_by_sale()
        res += """,
            partner.parent_id,
            partner.ref,
            partner.class_id,
            partner.state_id,
            partner.city """
        return res

