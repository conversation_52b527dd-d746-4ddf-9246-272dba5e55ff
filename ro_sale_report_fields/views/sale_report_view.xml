<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>
        <!-- Inherit the existing sale.report search view -->
        <record id="sale_report_searchview_inherited" model="ir.ui.view">
            <field name="name">sale.report.searchview.inherit</field>
            <field name="model">sale.report</field>
            <field name="inherit_id" ref="sale.view_order_product_search"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//field[@name='partner_id']" position="after">
                        <field name="parent_id" />
                        <field name="class_id" />
                    </xpath>
                    <xpath expr="//search//filter[@name='company']" position="after">
                        <separator/>
                        <filter string="Customer Code" name="partner_ref" domain="[]" context="{'group_by': 'partner_ref'}"/>
                        
                        <separator/>
                        <filter string="Main Partner" name="parent_id" domain="[]" context="{'group_by': 'parent_id'}"/>

                        <separator/>
                        <filter string="Customer Class" name="class_id" domain="[]" context="{'group_by': 'class_id'}"/>

                        <separator/>
                        <filter string="State" name="state_id" domain="[]" context="{'group_by': 'state_id'}"/>

                        <separator/>
                        <filter string="City" name="city" domain="[]" context="{'group_by': 'city'}"/>

                        <separator/>
                        <filter string="Payment Terms" name="payment_term_id" domain="[]" context="{'group_by': 'payment_term_id'}"/>
                    </xpath>
            
                </data>    
            </field>
        </record>

</data>
</odoo>
