from odoo import models, fields, api, _
from odoo.osv import expression
from odoo.exceptions import UserError, ValidationError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        """
        name search that supports searching by tag code
        """
        args = args or []
        domain = []
        if name:
            domain = ['|', ('registration_number', '=', name), ('name', operator, name)]
            if operator in expression.NEGATIVE_TERM_OPERATORS:
                domain = ['&'] + domain
        state = self.search(domain + args, limit=limit)
        return state.name_get()

    def name_get(self):
        employee_list = []
        for this in self:
            if this.registration_number:
                name = '%s [%s]' % (this.name or '', this.registration_number or '')
            else:
                name =  this.name or ''
            employee_list.append((this.id, name))
        return employee_list
    

    @api.constrains('ro_device_id', 'registration_number', 'name')
    def _check_unique_fields(self):
        for rec in self:
            domain = []

            if rec.ro_device_id:
                domain += [('ro_device_id', '=', rec.ro_device_id)]
            
            if rec.registration_number:
                if domain:
                    domain.insert(0, '|')

                domain += [('registration_number', '=', rec.registration_number)]

            if domain and self.search_count(domain) > 1:
                raise ValidationError(_("Registration Number and Device ID must be unique!"))
