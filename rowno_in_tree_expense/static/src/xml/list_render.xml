<?xml version="1.0" encoding="UTF-8" ?>
<template>
     <t
            t-name="rowno_in_tree_expense.HrExpenceRender"
            t-inherit="hr_expense.DashboardListRenderer"
            t-inherit-mode="extension"
            owl="1"
    >
        <xpath expr="//table/thead/tr/th[@t-if='hasSelectors']" position="before">
            <th>#</th>
        </xpath>
        <xpath expr="//div/table/tfoot/tr/td" position="before">
            <td/>
        </xpath>
    </t>

    <t
            t-name="rowno_in_tree_expense.HrExpenceRender_ListRenderer"
            t-inherit="hr_expense.ListRenderer"
            t-inherit-mode="extension"
            owl="1"
    >
        <xpath expr="//table/thead/tr/th[@t-if='hasSelectors']" position="before">
            <th>#</th>
        </xpath>
        <xpath expr="//div/table/tfoot/tr/td" position="before">
            <td/>
        </xpath>
    </t>

</template>