from odoo import models, fields
from odoo.exceptions import UserError

class MrpProdution(models.Model):
    _inherit = 'mrp.production'
    
    def button_mark_done(self):
        for rec in self:
            if len(rec.move_raw_ids)==0:
                raise UserError("The Manufacturing Order has No Components to Consume")
        return super(MrpProdution, self).button_mark_done()
    
    
    def action_confirm(self):
        for rec in self:
            if len(rec.move_raw_ids)==0:
                raise UserError("The Manufacturing Order has No Components to Consume") 
        return super(MrpProdution, self).action_confirm()
    