# -*- coding: utf-8 -*-
from odoo.exceptions import ValidationError
from odoo import models, fields, api,_


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    ro_credit_limit = fields.Float(string='Credit Limit', related='partner_id.credit_limit', readonly=True)
    ro_residual_credit = fields.Monetary(string='Residual Credit', compute='_compute_residual_credit', readonly=True)

    
    @api.depends('partner_id', 'partner_id.credit_limit', 'check_credit')
    def _compute_residual_credit(self):
        for order in self:
            partner = order.partner_id
            if order.check_credit:
                if partner.credit > partner.credit_limit:
                    raise ValidationError(_('Credit Limit Reached'))
                order.ro_residual_credit = partner.credit_limit - partner.credit
            else:
                order.ro_residual_credit = 0.0
