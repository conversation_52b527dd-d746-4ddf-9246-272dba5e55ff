# -*- coding: utf-8 -*-

import pytz
from datetime import datetime

from odoo import fields, models, _


class ZkMachineData(models.Model):
    _name = 'zk.machine.data'
    _description = 'ZK Machine Data'
    _order = 'create_date, id'

    ro_users = fields.Json(
        string='Users',
        readonly=True
    )

    ro_attendances = fields.Json(
        string='Attendances',
        readonly=True
    )

    company_id = fields.Many2one(
        string='Company',
        comodel_name='res.company',
        readonly=True
    )

    machine_id = fields.Many2one(
        string='Machine',
        comodel_name='zk.machine',
        readonly=True
    )

    def create_attendance_api(self, users, attendances, company_id, machine_id):
        self.create({
            'ro_users': users,
            'ro_attendances': attendances,
            'company_id': company_id,
            'machine_id': machine_id
        })

        return True

    def download_attendance_log(self):

        attendance = self.ro_attendances
        zk_attendance = self.env['zk.machine.attendance']

        for each in attendance:
            atten_time = each['DateTimeRecordFormated']

            try:
                atten_time = datetime.strptime(
                    atten_time, '%d/%m/%Y %H:%M:%S')
            except:
                atten_time = datetime.strptime(
                    atten_time, '%d-%m-%Y %H:%M:%S')

            local_tz = pytz.timezone(self.env.user.tz or 'GMT')

            local_dt = local_tz.localize(atten_time, is_dst=None)
            utc_dt = local_dt.astimezone(pytz.utc)
            utc_dt = utc_dt.strftime("%Y-%m-%d %H:%M:%S")
            atten_time = datetime.strptime(
                utc_dt, "%Y-%m-%d %H:%M:%S")
            atten_time = fields.Datetime.to_string(atten_time)

            if each['InOutMode'] not in (0,1):
                each['InOutMode'] = 9
                
            duplicate_atten_ids = zk_attendance.search(
                [('name', '=', str(each['IndRegID'])), ('punching_time', '=', atten_time)])

            if not duplicate_atten_ids:

                zk_attendance.create({
                    'name': str(each['IndRegID']),
                    'attendance_type': str(each['VerifyMode']),
                    'punch_type': str(each['InOutMode']),
                    'punching_time': atten_time,
                    'company_id': self.company_id.id,
                    'machine_id': self.machine_id.id})

    def cron_download(self):

        if not self:
            self = self.search([], limit=1)

        for rec in self:
            rec.download_attendance_log()

        self.unlink()
