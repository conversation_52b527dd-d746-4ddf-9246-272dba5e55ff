# -*- coding: utf-8 -*-

from dateutil import tz

from collections import defaultdict

from odoo import models, fields, _
from odoo.exceptions import UserError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    ro_device_id = fields.Char(string='Biometric Device ID',
                               help="Give the biometric device id")


class ZkMachine(models.Model):
    _name = 'zk.machine.attendance'
    _description = 'zk Machine Attendance'
    _order = 'name, punching_time, id'

    name = fields.Char(string='Biometric Device ID',
                       help="Biometric device id", required=True)

    punch_type = fields.Selection([('0', 'Check In'),
                                   ('1', 'Check Out'),
                                   ('9', 'Other')],
                                  string='Punching Type', default="0", required=True)

    attendance_type = fields.Selection([('1', 'Finger'),
                                        ('15', 'Face'),
                                        ('2', 'Type_2'),
                                        ('3', 'Password'),
                                        ('4', 'Card'),
                                        ('0', 'Other'),], string='Type', help="Select the attendance type",
                                       default="1", required=True)

    punching_time = fields.Datetime(
        string='Punching Time', help="Give the punching time", required=True)
    address_id = fields.Many2one(
        'res.partner', string='Working Address', help="Address")

    added = fields.Boolean(
        string='Added',
        help='Attendance has been created'
    )

    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.user.company_id.id)
    machine_id = fields.Many2one(
        string='Machine',
        comodel_name='zk.machine',
        readonly=True
    )

    def odoo_add_attendance(self):
        att_obj = self.env['hr.attendance']
        emp_obj = self.env['hr.employee']

        all_employees = emp_obj.search(
            [('ro_device_id', 'in', self.mapped('name'))])

        all_attendance = att_obj.search(
            [('ro_device_id', 'in', self.mapped('name'))])

        for attendance in self:

            employee = all_employees.filtered(
                lambda att: att.ro_device_id == attendance.name)

            if not employee:
                continue

            att_var = all_attendance.filtered(
                lambda att: att.ro_device_id == attendance.name and att.check_out == False)

            if attendance.punch_type == '0':  # check-in
                if att_var and attendance.punching_time > att_var[-1].check_in:
                    att_var[-1].related_zk_attendance_ids.added = False

                    att_var[-1].write({'check_in': attendance.punching_time,
                                      'related_zk_attendance_ids': attendance.ids})
                    attendance.added = True

                elif not att_var:
                    all_attendance += att_obj.create(
                        {'employee_id': employee.id, 'ro_device_id': attendance.name, 'check_in': attendance.punching_time, 'related_zk_attendance_ids': attendance.ids})

                    attendance.added = True

            elif attendance.punch_type == '1' and att_var:  # check-out
                att_var[-1].write({'check_out': attendance.punching_time,
                                  'related_zk_attendance_ids': att_var[-1].related_zk_attendance_ids.ids + attendance.ids})
                attendance.added = True
            else:
                raise UserError(_('Something Wrong.'))

    def add_attendance(self):
        if self.env['zk.machine.data'].search([]):
            raise UserError(_('All data must be downloaded first.'))

        # Create odoo attendance
        self.env['zk.machine.attendance'].search(
            [('added', '=', False)], limit=3000).odoo_add_attendance()

    def update_attendance_log(self):
        att_list = defaultdict(list)
        att_obj = self.env['zk.machine.attendance']

        self = self.env['zk.machine.attendance'].search([])

        for att in self:

            # Convert time zone
            local_date_time = att.punching_time.astimezone(
                tz.gettz(self.env.user.tz))

            if att_list[att.name] and (local_date_time - att_list[att.name]['date_time']).total_seconds()*1000 <= 900000:
                att_obj += att
                continue

            att_list[att.name] = {
                'type': not att_list[att.name]['type'] if att_list[att.name]
                and local_date_time.date() == att_list[att.name]['date_time'].date() else False,
                'date_time': local_date_time}

            att.punch_type = str(int(att_list[att.name]['type']))

        att_obj.unlink()
