from odoo import models, fields, api


class GamificationGoal(models.Model):
    _inherit = 'gamification.goal'

    ro_goal_weight = fields.Float(string="Weight %", related='challenge_id.line_ids.ro_goal_weight',store=True)
    


    ro_total_result = fields.Float(compute='_compute_ro_total_result', string='Total Result',store=True)

    @api.depends('ro_goal_weight', 'completeness')  # Watch for changes in these fields
    def _compute_ro_total_result(self):
        for goal in self:
            if goal.ro_goal_weight and goal.completeness:
                goal.ro_total_result = (goal.ro_goal_weight / 100) * goal.completeness  # Compute total result
            else:
                goal.ro_total_result = 0  # If any of the fields are missing, set result to 0
