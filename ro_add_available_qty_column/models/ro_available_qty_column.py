from odoo import models, fields, api

class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    qty_available = fields.Float(
        string='Qty Available',
        compute='_compute_qty_available',
        readonly=True,
    )

    @api.depends('product_id', 'order_id.picking_type_id.default_location_dest_id', 'order_id.picking_type_id.default_location_dest_id.usage')
    def _compute_qty_available(self):
        for line in self:
            line.qty_available = 0.0
            location_dest_id = line.order_id.picking_type_id.default_location_dest_id

            if location_dest_id:
                # Get all internal child locations of location_dest_id
                child_locations = self.env['stock.location'].search([
                    ('usage', '=', 'internal'),  # Ensure we're only looking at internal locations
                    ('id', 'child_of', location_dest_id.id)  # Find all child locations of location_dest_id
                ])

                # Search for stock quants in those internal child locations
                stock_quant = self.env['stock.quant'].search([
                    ('product_id', '=', line.product_id.id),
                    ('location_id', 'in', child_locations.ids)  # Search in all child locations
                ])
                # Sum the quantity of stock quants across all child locations
                line.qty_available = sum(stock_quant.mapped('quantity'))

                
            else:
                line.qty_available = 0.0


                    

# from odoo import models, fields, api

# class PurchaseOrderLine(models.Model):
#     _inherit = 'purchase.order.line'

#     qty_available = fields.Float(
#         string='Qty Available',
#         compute='_compute_qty_available',
#         readonly=True,
#         store=True, 
#     )

#     @api.depends('product_id', 'order_id.picking_type_id.default_location_dest_id')
#     def _compute_qty_available(self):
#         for line in self:
#             line.qty_available = 0.0
#             if line.order_id.picking_type_id.default_location_dest_id:
#                 location_dest_id = line.order_id.picking_type_id.default_location_dest_id
#                 stock_quant = self.env['stock.quant'].search([
#                     ('product_id', '=', line.product_id.id),
#                     ('location_id', '=', location_dest_id.id)
#                 ])
#                 line.qty_available = sum(stock_quant.mapped('quantity'))
#             else:
#                 line.qty_available = 0.0
