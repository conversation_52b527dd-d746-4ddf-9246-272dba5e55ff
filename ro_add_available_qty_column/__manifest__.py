# -*- coding: utf-8 -*-
{
    'name': "Add Available Qty",

    'summary': """
       Add Available Qty in RFQ Lines""",

    'description': """
        Add Available Qty in RFQ Lines
    """,

    'author': "Roayatec",
    'website': "https://www.roayatec.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/16.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'purchase',
    'version': '16.0',

    # any module necessary for this one to work correctly
    'depends': ['base','purchase','stock','purchase_stock'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/ro_available_qty_column_view.xml',
  
    ],
    'installable': True,
    'license': 'OPL-1',
    
}
