# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, Command
from odoo.exceptions import UserError
from odoo.addons.purchase.models.purchase import PurchaseOrder


# @api.model_create_multi
# def create(self, vals_list):
#     orders = self.browse()
#     partner_vals_list = []
#     for vals in vals_list:
#         company_id = vals.get('company_id', self.default_get(['company_id'])['company_id'])
#         # Ensures default picking type and currency are taken from the right company.
#         self_comp = self.with_company(company_id)
#         if vals.get('name', 'New') == 'New':
#             seq_date = None
#             if 'date_order' in vals:
#                 seq_date = fields.Datetime.context_timestamp(self, fields.Datetime.to_datetime(vals['date_order']))
#             vals['name'] = self_comp.env['ir.sequence'].next_by_code('purchase.request', sequence_date=seq_date) or '/'
#         vals, partner_vals = self._write_partner_values(vals)
#         partner_vals_list.append(partner_vals)
#         orders |= super(PurchaseOrder, self_comp).create(vals)
#     for order, partner_vals in zip(orders, partner_vals_list):
#         if partner_vals:
#             order.sudo().write(partner_vals)  # Because the purchase user doesn't have write on `res.partner`
#     return orders

# PurchaseOrder.create = create

def button_confirm(self):
    for order in self:
        if order.state not in ['draft', 'sent','sec_approve']:
            continue
        order.order_line._validate_analytic_distribution()
        order._add_supplier_to_product()
        # Deal with double validation process
        if order._approval_allowed():
            order.button_approve()
        else:
            order.write({'state': 'to approve'})
        if order.partner_id not in order.message_partner_ids:
            order.message_subscribe([order.partner_id.id])
    return True
PurchaseOrder.button_confirm = button_confirm

class PurchaseOrderApprovre(models.Model):
    _inherit = 'purchase.order'

    def submit_first_approve(self):
        for rec in self:
            rec.state = 'first_approve'

    def sec_approve_purchase(self):
        for rec in self:
            rec.state = 'sec_approve'
    
    state = fields.Selection(selection_add=[
        ('first_approve', 'First Approve'),
        ('sec_approve', 'Sec Approve'),
        ('sent',),
    ])

