<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="purchase_order_approval_inherit" model="ir.ui.view">
            <field name="name">purchase.order.approval</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="/form/header/field[@name='state']"  position="replace">
                    <field name="state" widget="statusbar" statusbar_visible="draft,sent,first_approve,sec_approve,purchase"/>
                </xpath>
                <xpath expr="//button[@name='button_cancel'][1]" position="replace">
                    <button name="button_cancel" states="first_approve,sec_approve,draft,to approve,sent,purchase" string="Cancel" type="object" data-hotkey="x"/>
                </xpath>
                <xpath expr="//button[@name='button_confirm'][2]" position="replace">
                    <!---->
                </xpath>
                <xpath expr="//button[@name='button_confirm'][1]" position="replace">
                    <!---->
                </xpath>

                <xpath expr="//header" position="inside">
                    <button name="submit_first_approve" string="First Approval" type="object" class="oe_highlight" states="draft,sent"/>
                    
                    <button name="sec_approve_purchase" string="Sec Approve" type="object" class="oe_highlight" states="first_approve" />

                    <button name="button_confirm" type="object" states="sec_approve" context="{'validate_analytic': True}" string="Confirm Order" id="draft_confirm"/>
                </xpath>
    
            </field>
        </record>
        
    </data>
</odoo>