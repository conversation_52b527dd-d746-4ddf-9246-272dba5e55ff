import logging

from odoo import models, fields, api,_
from odoo import tools
from datetime import datetime
from num2words import num2words
from odoo.addons.stock.models.stock_picking import Picking

from odoo.exceptions import UserError, ValidationError

class StockWarehouse(models.Model):

    _inherit = 'stock.warehouse'

    crm_team_id = fields.Many2one(
        string='CRM Team',
        comodel_name='crm.team'
    )
    

# @api.depends('picking_type_id', 'partner_id')
# def _compute_location_id(self):
#     for picking in self:
#         picking = picking.with_company(picking.company_id)
#         if picking.picking_type_id and picking.state == 'draft' and not picking.is_transfer_in and not picking.is_transfer_out:
#             if picking.picking_type_id.default_location_src_id:
#                 location_id = picking.picking_type_id.default_location_src_id.id
#             elif picking.partner_id:
#                 location_id = picking.partner_id.property_stock_supplier.id
#             else:
#                 _customerloc, location_id = self.env['stock.warehouse']._get_partner_locations()

#             if picking.picking_type_id.default_location_dest_id:
#                 location_dest_id = picking.picking_type_id.default_location_dest_id.id
#             elif picking.partner_id:
#                 location_dest_id = picking.partner_id.property_stock_customer.id
#             else:
#                 location_dest_id, _supplierloc = self.env['stock.warehouse']._get_partner_locations()

#             picking.location_id = location_id
#             picking.location_dest_id = location_dest_id

#             print("picking.location_dest_id")
#             print(picking.location_dest_id)
#             print(location_dest_id)
#             print("picking.location_dest_id")
# Picking._compute_location_id = _compute_location_id

class StockPicking(models.Model):
    _inherit="stock.picking"

    is_transfer_in = fields.Boolean()
    is_transfer_out = fields.Boolean()

    # warehouse_team_id = fields.Many2one(
    #     'crm.team', 'Warehouse User', compute='_compute_warehouse_team_id', store=True)
    
    # warehouse_team_from_id = fields.Many2one(
    #     'crm.team', 'Warehouse User From', compute='_compute_warehouse_team_id', store=True)
    # warehouse_team_to_id = fields.Many2one(
    #     'crm.team', 'Warehouse User TO', compute='_compute_warehouse_team_id', store=True)
    

    # @api.depends('picking_type_id','location_id','location_dest_id')
    # def _compute_warehouse_team_id(self):
    #     for picking in self:

    #         picking.warehouse_team_id = False
    #         picking.warehouse_team_from_id = False
    #         picking.warehouse_team_to_id = False
    #         warehouse = picking.picking_type_id.warehouse_id
    
    #         warehouse_from = picking.location_id.warehouse_id
    #         warehouse_to = picking.location_dest_id.warehouse_id

    #         if warehouse:
    #             picking.warehouse_team_id = warehouse.crm_team_id
    #         if warehouse_from:
    #             picking.warehouse_team_from_id = warehouse_from.crm_team_id
    #         if warehouse_to:
    #             picking.warehouse_team_to_id = warehouse_to.crm_team_id
    
    @api.onchange('is_transfer_in','is_transfer_out')
    def add_default_operation_type(self):
        for this in self:
            location = self.env['stock.location'].search([('location_user_ids','=',self.env.user.id)], limit=1)

            if len(location)>0 :
                warehouse_id = location[0].warehouse_id
                if this.is_transfer_in:
                    this.picking_type_id = self.env['stock.picking.type'].search([('code','=','internal'),('warehouse_id','=', warehouse_id.id)],limit=1) 
                    this.location_dest_id = location.id
                if this.is_transfer_out:
                    this.picking_type_id = self.env['stock.picking.type'].search([('code','=','internal'),('warehouse_id','=', warehouse_id.id)],limit=1)        
                    this.location_id = location.id
        
    @api.depends('picking_type_id', 'partner_id')
    def _compute_location_id(self):
        for picking in self:
            if picking.picking_type_code != 'internal' and not (picking.is_transfer_in or picking.is_transfer_out):
                res = super(StockPicking, picking)._compute_location_id()

            elif (picking.is_transfer_in or picking.is_transfer_out):
                location = self.env['stock.location'].search([('location_user_ids','=',self.env.user.id)])
                if len(location)>0:
                    location_id = location[0].id
                elif picking.picking_type_id.default_location_src_id:
                    location_id = picking.picking_type_id.default_location_src_id.id
                elif picking.partner_id:
                    location_id = picking.partner_id.property_stock_supplier.id
                else:
                    customerloc, location_id = self.env['stock.warehouse']._get_partner_locations()
                
                if picking.picking_type_id.default_location_dest_id:
                    location_dest_id = picking.picking_type_id.default_location_dest_id.id
                elif picking.partner_id:
                    location_dest_id = picking.partner_id.property_stock_customer.id
                else:
                    location_dest_id, _supplierloc = self.env['stock.warehouse']._get_partner_locations()

                if picking.is_transfer_in:
                    picking.location_id = picking.location_id or location_id
                if picking.is_transfer_out:
                    picking.location_dest_id = picking.location_dest_id or location_dest_id

    
    # @api.onchange('picking_type_id', 'partner_id')
    # def _onchange_picking_type(self):
    #     if self.picking_type_code != 'internal' and not (self.is_transfer_in or self.is_transfer_out):
    #         res = super(StockPicking, self)._onchange_picking_type()
    #     elif (self.is_transfer_in or self.is_transfer_out):
    #         location = self.env['stock.location'].search([('location_user_ids','=',self.env.user.id)])
    #         if len(location)>0:
    #                 location_id = location[0].id
    #         elif self.picking_type_id.default_location_src_id:
    #             location_id = self.picking_type_id.default_location_src_id.id
    #         elif self.partner_id:
    #             location_id = self.partner_id.property_stock_supplier.id
    #         else:
    #             customerloc, location_id = self.env['stock.warehouse']._get_partner_locations()
            # if not self.is_transfer_out:
            #     self.location_id = location_id

    @api.onchange('location_dest_id')
    def get_operation_type(self):
        for this in self:
            if this.picking_type_code == 'internal' or this.is_transfer_in or this.is_transfer_out:
                warehouse = this.location_dest_id.warehouse_id
                picking_types = warehouse.int_type_id
                # self.env['stock.picking.type'].search([('code','=','internal'), ('default_location_dest_id','=',this.location_dest_id.id)])
                this.picking_type_id = picking_types
                # if len(picking_types)>0 else False
