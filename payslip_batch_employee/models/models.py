# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.osv import expression


class ContactInherited(models.Model):
    _inherit = 'hr.contract'

    tag_id = fields.Many2one('hr.employee.category')

class HrPayslipEmployee(models.TransientModel):
    _inherit = 'hr.payslip.employees'

    tag_id = fields.Many2one('hr.employee.category')

    division_ids = fields.Many2many('employee.division')

    @api.onchange('tag_id','division_ids')
    def _onchange_tag_id(self):
        # all_ids = self.env['hr.employee'].search([])
        for this in self:
            domain = this._get_available_contracts_domain()
            if this.tag_id:
                domain = expression.AND([
                    domain,
                    [('contract_ids.tag_id', '=', this.tag_id.id)]
                ])
            if len(this.division_ids)>0:
                domain = expression.AND([
                    domain,
                    [('ro_division_id', 'in', this.division_ids.ids)]
                ])

            this.employee_ids = [(5,0,0)]

            this.employee_ids = self.env['hr.employee'].search(domain)

            # this.employee_ids = [(5,0,0)]
            # if this.tag_id != False:
            #     this.employee_ids = [(6,0,all_ids.filtered(lambda x: this.tag_id.id in x.category_ids.ids).mapped('id'))]
            # else:
            #     this.employee_ids = [(6,0,all_ids.mapped('id'))]
