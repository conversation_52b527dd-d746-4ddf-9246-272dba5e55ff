<?xml version="1.0"?>
<odoo>
    <data>
        <template id="report_saleorder_document_detailed" inherit_id="sale.report_saleorder_document">
            <xpath expr="//div[@name='so_total_summary']/div[@name='total']//table/t[@t-call='account.document_tax_totals']" position="before">
                <tr class="border-black o_subtotal">
                    <td><strong>Subtotal</strong></td>

                    <td class="text-end">
                        <span
                            t-att-class="oe_subtotal_footer_separator"
                            t-esc="doc.total_price" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'
                        />
                    </td>
                </tr>
                <tr class="border-black o_subtotal">
                    <td><strong>Discount Amt</strong></td>

                    <td class="text-end">
                        <span
                            t-att-class="oe_subtotal_footer_separator"
                            t-esc="doc.py_total_discount" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'
                        />
                    </td>
                </tr>
            </xpath>
        </template>
        

        <template id="report_invoice_document_detailed" inherit_id="account.report_invoice_document">
            <xpath expr="//div[@id='total']//table/t[@t-call='account.document_tax_totals']" position="before">
                <tr class="border-black o_subtotal">
                    <td><strong>Subtotal</strong></td>

                    <td class="text-end">
                        <span
                            t-att-class="oe_subtotal_footer_separator"
                            t-esc="o.total_amount_before_discount" t-options='{"widget": "monetary", "display_currency": o.currency_id}'
                        />
                    </td>
                </tr>
                <tr class="border-black o_subtotal">
                    <td><strong>Discount Amt</strong></td>

                    <td class="text-end">
                        <span
                            t-att-class="oe_subtotal_footer_separator"
                            t-esc="o.py_total_discount" t-options='{"widget": "monetary", "display_currency": o.currency_id}'
                        />
                    </td>
                </tr>
            </xpath>
        </template>
    </data>
</odoo>
