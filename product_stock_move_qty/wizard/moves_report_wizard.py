# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, exceptions
from odoo.exceptions import UserError
from datetime import datetime, time



class WizardProductMoves(models.TransientModel):
    _name = 'wizard.product.stock.moves.loc'
    _description = 'wizard Product Stock report'
  
    product_ids = fields.Many2many('product.product')

    location_ids = fields.Many2many('stock.location',domain="[('usage', '=', 'internal')]")

    date_from = fields.Date(default=datetime.today())
    date_to = fields.Date(default=datetime.today())


    def action_get_data(self):

        view_id = self.env.ref('product_stock_move_qty.view_location_action_wizard_inherited').id
        domain = []

        if self.product_ids:
            domain.append(('product_id','in',self.product_ids.ids))
        if self.location_ids:
            domain.append(('location_id','in',self.location_ids.ids))
        if self.date_from:
            domain.append(('date','>=',datetime.combine(self.date_from, time.min)))
        if self.date_to:
            domain.append(('date','<=',datetime.combine(self.date_to, time.max)))
        if len(domain) > 0:
            return {
                    'name': _('Product Moves'),
                    'view_mode': 'tree',
                    'res_model': 'stock.move.location',
                    'domain': domain,
                    'context': {'group_by':['location_id']},
                    'view_id': view_id,
                    'type': 'ir.actions.act_window'
                }
        else:
            return {
                    'name': _('Product Moves'),
                    'view_mode': 'tree',
                    'res_model': 'stock.move.location',
                    'context': {'group_by':['location_id']},
                    'view_id': view_id,
                    'type': 'ir.actions.act_window'
                }