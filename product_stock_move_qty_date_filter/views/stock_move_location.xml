<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="stock_move_location_filter_view_search" model="ir.ui.view">
            <field name="name">StockMoveLocationSearch</field>
            <field name="model">stock.move.location.filter</field>
            <field name="arch" type="xml">
                <search string="Stock Moves">
                    <field name="product_id"/>
                    <field name="partner_id"/>
                    <field name="origin"/>
                    <field name="analytic_account_id"/>
                    <field name="location_id" string="Location"/>
                    <field name="date"/>
                    <field name="location_name_to" string="Location To"/>
                    <field name="location_name_from" string="Location From"/>
                    <field name="warehouse_id" string="Warehouse"/>
                    <filter string="Warehouse" name="group_by_warehouse" icon="terp-gtk-jump-to-rtl" domain="[]" context="{'group_by':'warehouse_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Location" name="group_by_location" icon="terp-gtk-jump-to-rtl" domain="[]" context="{'group_by':'location_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Category" name="group_by_category" icon="terp-gtk-jump-to-rtl" domain="[]" context="{'group_by':'categ_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Product" name="group_by_product" icon="terp-accessories-archiver" domain="[]"  context="{'group_by':'product_id'}"/>
                    
                </search>
            </field>
        </record>
        <!-- <record id="filter_location_product" model="ir.filters">
            <field name="name">Location</field>
            <field name="model_id">stock.move.location.filter</field>
            <field name="context">{'group_by': ['location_id', 'product_id']}</field>
        </record>
        <record id="filter_product_productlo" model="ir.filters">
            <field name="name">Product</field>
            <field name="model_id">stock.move.location.filter</field>
            <field name="context">{'group_by': ['product_id', 'location_id']}</field>
        </record> -->
        <record id="stock_move_location_filter_view_tree" model="ir.ui.view">
            <field name="name">StockMoveLocationViewTree</field>
            <field name="model">stock.move.location.filter</field>
            <field name="arch" type="xml">
                <tree string="Location Product Moves">
                    <field name="warehouse_id" invisible="1"/>
                    <field name="date"/>
                    <field name="product_id"/>
                    <field name="partner_id"  optional="show"/>
                    <field name="origin"  optional="show"/>
                    <field name="analytic_account_id"  optional="show"/>
                    <field name="location_id" invisible="1"/>
                    <field name="location_name_from" optional="show"/>
                    <field name="location_name_to" optional="show"/>
                    <field name="move_type"  optional="show"/>
                    <field name="description"  optional="show"/>
                    <field name="categ_id" invisible="1"/>
                    <field name="uom_id" invisible="1"/>
                    <field name="qty_add" sum="Qty Add"  optional="show"/>
                    <field name="qty_ded" sum="Qty Ded"  optional="show"/>
                    <field name="value" attrs="{'invisible':[('move_type', '=', 'internal')]}" sum="Value"  optional="show"/>
                    <field name="total_value" attrs="{'invisible':[('move_type', '=', 'internal')]}" sum="Total Val"  optional="show"/>
                    <field name="qty_current" sum="Qty Curr"  optional="show"/>
                    <field name="qty_on_hand" sum="Qty Total Curr"  optional="show"/>
                    <field name="total_value_balance" attrs="{'invisible':[('move_type', '=', 'internal')]}" sum="Total Val Bal"  optional="show"/>
                </tree>
            </field>
        </record>
        <!-- <record id="product_stock_move_location_filter_rule" model="ir.rule">
            <field name="name">product stock move location multi-company</field>
            <field name="model_id" ref="model_stock_move_location"/>
            <field eval="True" name="global"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
        </record> -->
        <record id="stock_move_location_filter_action" model="ir.actions.act_window" >
            <field name="name">Product Moves Location</field>
            <field name="res_model">stock.move.location.filter</field>
            <field name="view_mode">tree</field>
            <!--<field name="context">{'search_default_group_by_location': 1}</field>-->
            <field name="context">{'group_by':['warehouse_id', 'product_id']}</field>
            <!-- <field name="domain">['|',('company_id','=',False),('company_id', 'in', allowed_company_ids)]</field> -->
            <field name="view_id" ref="stock_move_location_filter_view_tree"/>
            <field name="search_view_id" ref="stock_move_location_filter_view_search"/>
        </record>
        <record id="stock_move_location_filter_action_detaild" model="ir.actions.act_window" >
            <field name="name">Product Moves Location Detailed</field>
            <field name="res_model">stock.move.location.filter</field>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="stock_move_location_filter_view_tree"/>
            <field name="search_view_id" ref="stock_move_location_filter_view_search"/>
        </record>
        <!-- <record id="stock_move_location_filter_action_product_tmpl_moves" model="ir.actions.act_window">
            <field name="name">Location Moves</field>
            <field name="res_model">stock.move.location.filter</field>   
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{'group_by':['location_id']}</field>
        </record>

        <record id="stock_move_location_filter_action_product_tmpl_moves" model="ir.actions.act_window">
            <field name="name">Location Moves</field>
            <field name="res_model">stock.move.location.filter</field>   
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{'group_by':['location_id']}</field>
        </record> -->
        <act_window
                id="stock_move_location_filter_action_product_tmpl_moves"
                context="{'group_by': 'location_id'}"
                domain="[('product_tmpl_id', 'in', [active_id])]"
                name="Location Moves"
                res_model="stock.move.location.filter"
                binding_model="product.template"
                target="current"
        />
        <!-- <menuitem id="menu_action_stock_move_location_filter_report" name="Stock Product Move" action="stock_move_location_filter_action" sequence="160" parent="stock.menu_warehouse_report"/>
        <menuitem id="menu_action_stock_move_location_filter_detailed_report" name="Detailed Stock Product Move" action="stock_move_location_filter_action_detaild" sequence="161" parent="stock.menu_warehouse_report"/> -->
    </data>
</odoo>