# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, models


class AccountInvoiceReport(models.Model):
    _inherit = 'account.invoice.report'

    amount_residual = fields.Float(string="Amount Due")
    value = fields.Float('Cost', readonly=True)

    def _select(self):
        return super()._select() + ", (case when move.amount_total != 0 then ((line.price_total/move.amount_total) * move.amount_residual) else 0 end) as amount_residual,    (CASE WHEN (line.cost * line.quantity)>0 THEN (line.cost * line.quantity * (CASE WHEN move.move_type IN ('in_invoice','out_refund','in_receipt') THEN -1 ELSE 1 END)) ELSE 0.0 END) as value"
