# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from datetime import datetime, timedelta


class AccountMoveLineInherit(models.Model):
    _inherit = 'account.move.line'

    cost = fields.Float()


    def create(self, values):
        res = super(AccountMoveLineInherit, self).create(values)

        stock_valuation_obj = self.env['stock.valuation.layer']
        for record in res:
            if record.move_id and record.move_id.line_ids and record.move_id.invoice_origin:
                if record.move_id.move_type in ('out_invoice', 'out_refund', 'out_receipt'):
                    
                    if record.move_id.landed_costs_ids and line.quantity:
                        move_line_ids = record.move_id.landed_costs_ids.account_move_id.line_ids.filtered(
                            lambda x: x.product_id == record.product_id and x.balance > 0
                        )
                        cost = 0.0
                        for line in move_line_ids:
                            cost += line.balance

                        record.cost = cost / line.quantity
                    else:
                        stock_move_ids = record.sale_line_ids.move_ids or record.purchase_line_id.move_ids
                        stock_move_ids = stock_move_ids.filtered(lambda x:x.state == 'done')
                        if stock_move_ids:
                            if stock_move_ids:
                                valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_ids[:1].id)])
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                product_cost = 0.0
                            record.cost = product_cost
                elif record.move_id.move_type in ('in_invoice', 'in_refund', 'in_receipt'):
                    record.cost = record.price_unit
                else:
                    record.cost = record.product_id.standard_price
            else:
                    record.cost = record.product_id.standard_price
        return res

    @api.model
    def cron_compute_invoice_product_cost(self):
        move_line_ids = self.search([('move_id.move_type', 'in', ('out_invoice', 'out_refund', 'out_receipt')),
                                     ('id', '<=', 40000),
                                     ('product_id', '!=', False)])
        
        stock_move_obj = self.env['stock.move']
        account_move_obj = self.env['account.move']
        stock_valuation_obj = self.env['stock.valuation.layer']
        for move_line in move_line_ids:
            if move_line.move_id and move_line.move_id.invoice_origin:
                if move_line.move_id.landed_costs_ids and move_line.quantity:
                    move_line_ids = move_line.move_id.landed_costs_ids.account_move_id.line_ids.filtered(
                        lambda x: x.product_id == move_line.product_id and x.balance > 0
                    )
                    cost = 0.0
                    for line in move_line_ids:
                        cost += line.balance

                    self.env.cr.execute(
                        """UPDATE account_move_line SET cost = """ + str(
                            cost / move_line.quantity) + """ WHERE id = """ + str(
                            move_line.id))
                else:
                    stock_move_ids = stock_move_obj.search([('origin', '=', str(move_line.move_id.invoice_origin)),
                                                            ('state', '=', 'done')])
                    if stock_move_ids:
                        stock_move_id = stock_move_ids.filtered(lambda x: x.product_id == move_line.product_id and \
                                                                          x.product_uom_qty == move_line.quantity)
                        if len(stock_move_id) > 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id[0].id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id[0].id)], limit=1)
                                product_cost = entry.amount_total

                        elif len(stock_move_id) == 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id.id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id.id)], limit=1)
                                product_cost = entry.amount_total
                        else:
                            product_cost = 0.0

                        self.env.cr.execute(
                            """UPDATE account_move_line SET cost = """ + str(product_cost) + """ WHERE id = """ + str(
                                move_line.id))
            else: 
                move_line.write({'cost':move_line.product_id.standard_price}) 

        move_line_ids = self.search([('move_id.move_type', 'in', ('in_invoice', 'in_refund', 'in_receipt')),
                                     ('id', '<=', 40000),
                                     ('product_id', '!=', False)])
        for rec in move_line_ids:
            rec.write({'cost':rec.price_unit})

        move_line_ids_cost_not_set = self.search([('move_id.move_type', 'in', ('in_invoice', 'in_refund', 'in_receipt','out_invoice', 'out_refund', 'out_receipt')),
                                     ('id', '<=', 40000),
                                     ('product_id', '!=', False), ('cost', '=', False)])
        for rec in move_line_ids_cost_not_set:
            rec.write({'cost':rec.product_id.standard_price})

    @api.model
    def cron_compute_invoice_product_cost_part2(self):
        move_line_ids = self.search([('move_id.move_type', 'in', ('out_invoice', 'out_refund', 'out_receipt',)),
                                     ('id', '>', 40000), ('id', '<=', 100000),
                                     ('product_id', '!=', False)])
        
        stock_move_obj = self.env['stock.move']
        account_move_obj = self.env['account.move']
        stock_valuation_obj = self.env['stock.valuation.layer']
        for move_line in move_line_ids:
            if move_line.move_id and move_line.move_id.invoice_origin:
                if move_line.move_id.landed_costs_ids and move_line.quantity:
                    move_line_ids = move_line.move_id.landed_costs_ids.account_move_id.line_ids.filtered(
                        lambda x: x.product_id == move_line.product_id and x.balance > 0
                    )
                    cost = 0.0
                    for line in move_line_ids:
                        cost += line.balance

                    self.env.cr.execute(
                        """UPDATE account_move_line SET cost = """ + str(
                            cost / move_line.quantity) + """ WHERE id = """ + str(
                            move_line.id))
                else:
                    stock_move_ids = stock_move_obj.search([('origin', '=', str(move_line.move_id.invoice_origin)),
                                                            ('state', '=', 'done')])
                    if stock_move_ids:
                        stock_move_id = stock_move_ids.filtered(lambda x: x.product_id == move_line.product_id and \
                                                                          x.product_uom_qty == move_line.quantity)
                        if len(stock_move_id) > 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id[0].id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id[0].id)], limit=1)
                                product_cost = entry.amount_total

                        elif len(stock_move_id) == 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id.id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id.id)], limit=1)
                                product_cost = entry.amount_total
                        else:
                            product_cost = 0.0

                        self.env.cr.execute(
                            """UPDATE account_move_line SET cost = """ + str(product_cost) + """ WHERE id = """ + str(
                                move_line.id))
            else: 
                move_line.write({'cost':0}) 

        move_line_ids = self.search([('move_id.move_type', 'in', ('in_invoice', 'in_refund', 'in_receipt')),
                                     ('id', '>', 40000), ('id', '<=', 100000),
                                     ('product_id', '!=', False)])
        for rec in move_line_ids:
            rec.write({'cost':rec.price_unit})

    @api.model
    def cron_compute_invoice_product_cost_part3(self):
        move_line_ids = self.search([('move_id.move_type', 'in', ('out_invoice', 'out_refund', 'out_receipt')),
                                     ('id', '>', 100000), ('id', '<=', 150000),
                                     ('product_id', '!=', False)])
        
        stock_move_obj = self.env['stock.move']
        account_move_obj = self.env['account.move']
        stock_valuation_obj = self.env['stock.valuation.layer']
        for move_line in move_line_ids:
            if move_line.move_id and move_line.move_id.invoice_origin:
                if move_line.move_id.landed_costs_ids and move_line.quantity:
                    move_line_ids = move_line.move_id.landed_costs_ids.account_move_id.line_ids.filtered(
                        lambda x: x.product_id == move_line.product_id and x.balance > 0
                    )
                    cost = 0.0
                    for line in move_line_ids:
                        cost += line.balance

                    self.env.cr.execute(
                        """UPDATE account_move_line SET cost = """ + str(
                            cost / move_line.quantity) + """ WHERE id = """ + str(
                            move_line.id))
                else:
                    stock_move_ids = stock_move_obj.search([('origin', '=', str(move_line.move_id.invoice_origin)),
                                                            ('state', '=', 'done')])
                    if stock_move_ids:
                        stock_move_id = stock_move_ids.filtered(lambda x: x.product_id == move_line.product_id and \
                                                                          x.product_uom_qty == move_line.quantity)
                        if len(stock_move_id) > 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id[0].id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id[0].id)], limit=1)
                                product_cost = entry.amount_total

                        elif len(stock_move_id) == 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id.id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id.id)], limit=1)
                                product_cost = entry.amount_total
                        else:
                            product_cost = 0.0

                        self.env.cr.execute(
                            """UPDATE account_move_line SET cost = """ + str(product_cost) + """ WHERE id = """ + str(
                                move_line.id))
            else: 
                move_line.write({'cost':0}) 

        move_line_ids = self.search([('move_id.move_type', 'in', ('in_invoice', 'in_refund', 'in_receipt')),
                                     ('id', '>', 100000), ('id', '<=', 150000),
                                     ('product_id', '!=', False)])
        for rec in move_line_ids:
            rec.write({'cost':rec.price_unit})

    @api.model
    def cron_compute_invoice_product_cost_part4(self):
        move_line_ids = self.search([('move_id.move_type', 'in', ('out_invoice', 'out_refund', 'out_receipt')),
                                     # ('create_date', '>', str(datetime.today() + timedelta(days=-10))),
                                     # ('cost', '=', 0),
                                     ('cost', '=', False),
                                     ('product_id', '!=', False)])
        stock_move_obj = self.env['stock.move']
        account_move_obj = self.env['account.move']
        stock_valuation_obj = self.env['stock.valuation.layer']
        for move_line in move_line_ids:
            if move_line.move_id and move_line.move_id.invoice_origin:
                if move_line.move_id.landed_costs_ids and move_line.quantity:
                    move_line_ids = move_line.move_id.landed_costs_ids.account_move_id.line_ids.filtered(
                        lambda x: x.product_id == move_line.product_id and x.balance > 0
                    )
                    cost = 0.0
                    for line in move_line_ids:
                        cost += line.balance

                    self.env.cr.execute(
                        """UPDATE account_move_line SET cost = """ + str(
                            cost / move_line.quantity) + """ WHERE id = """ + str(
                            move_line.id))
                else:
                    stock_move_ids = stock_move_obj.search([('origin', '=', str(move_line.move_id.invoice_origin)),
                                                            ('state', '=', 'done')])
                    if stock_move_ids:
                        stock_move_id = stock_move_ids.filtered(lambda x: x.product_id == move_line.product_id and \
                                                                          x.product_uom_qty == move_line.quantity)
                        if len(stock_move_id) > 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id[0].id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id[0].id)], limit=1)
                                product_cost = entry.amount_total

                        elif len(stock_move_id) == 1:
                            valuation_id = stock_valuation_obj.search([('stock_move_id', '=', stock_move_id.id)])
                            if valuation_id:
                                quantity = abs(sum(valuation_id.mapped('quantity')))
                                product_cost = abs(sum(valuation_id.mapped('value'))) / (quantity if quantity != 0 else 1)
                            else:
                                entry = account_move_obj.search([('stock_move_id', '=', stock_move_id.id)], limit=1)
                                product_cost = entry.amount_total
                        else:
                            product_cost = 0.0
                        self.env.cr.execute(
                            """UPDATE account_move_line SET cost = """ + str(product_cost) + """ WHERE id = """ + str(
                                move_line.id))

