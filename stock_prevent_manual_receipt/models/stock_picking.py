from odoo import models
from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    _inherit = "stock.picking"
    
    def action_confirm(self):
        for rec in self:
            if rec.picking_type_code == 'incoming' and not rec.picking_type_id.is_return and not rec.purchase_id:
                raise ValidationError("Can't Confirm a Receive that doesn't have a PO")
        return super().action_confirm()