# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID


class EmployeeEffects(models.Model):
    _name = 'employee.effects'
    _description = 'Employee Effects'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    name = fields.Char(string='Name', required=True, copy=False, default=(_('New')), readonly=True)


    employee_id = fields.Many2one('hr.employee', required=True)
    
    time_off_id = fields.Many2one('hr.leave.type', domain=[('is_employee_effect','=',True)])
    date = fields.Date(default=fields.Date.context_today)
    duration = fields.Float()

    duration_type = fields.Selection([('days','Days'),('hours','Hours')], default='days')

    note = fields.Text('Description')
    @api.model
    def create(self,vals):
        vals['name'] = self.env['ir.sequence'].with_user(SUPERUSER_ID).next_by_code('employee.effects') or "/"
        new_record = super(EmployeeEffects, self).create(vals)
        return new_record