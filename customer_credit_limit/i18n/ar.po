# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* emkay_customer_credit_limit
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-08 20:17+0000\n"
"PO-Revision-Date: 2021-02-08 20:17+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: emkay_customer_credit_limit
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_sale_order__check_credit
msgid "Check Credit"
msgstr "التحقق من الدائن"

#. module: emkay_customer_credit_limit
#: model:ir.model,name:emkay_customer_credit_limit.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: emkay_customer_credit_limit
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_res_partner__credit_limit
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_res_users__credit_limit
msgid "Credit Limit"
msgstr "حد الدائن"

#. module: emkay_customer_credit_limit
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_res_partner__display_name
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_sale_order__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: emkay_customer_credit_limit
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_res_partner__id
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_sale_order__id
msgid "ID"
msgstr "المُعرف"

#. module: emkay_customer_credit_limit
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_res_partner____last_update
#: model:ir.model.fields,field_description:emkay_customer_credit_limit.field_sale_order____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: emkay_customer_credit_limit
#: model:ir.model,name:emkay_customer_credit_limit.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"
