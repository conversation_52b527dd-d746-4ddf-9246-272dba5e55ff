# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    check_credit = fields.Boolean('Check Credit', default=True, copy=False, tracking=10)

    check_flag = fields.Boolean(default=False, compute='_compute_credit_limit_check')

    
    @api.depends('name', 'create_date')
    def _compute_credit_limit_check(self):
        for rec in self:
            user_check_flag = self.env['res.users'].has_group('customer_credit_limit.group_credit_limit_manager_cost')
            
            if user_check_flag == True:
                rec.check_flag = True
                
            else:
                rec.check_flag = False

    def action_confirm(self):
        partner = False
        if self.partner_id and self.partner_id.parent_id:
            partner = self.partner_id.parent_id
        elif self.partner_id:
            partner = self.partner_id
        if partner.credit + (self.amount_total*self.currency_rate) > partner.credit_limit and self.check_credit:
            raise UserError(_('Credit Limit Reached'))
        
        return super(<PERSON>Order, self).action_confirm()
        
