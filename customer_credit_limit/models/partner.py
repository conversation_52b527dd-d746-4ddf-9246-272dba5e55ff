# -*- coding: utf-8 -*-

from odoo import models, fields, api, _

class ResPartner(models.Model):
    _inherit = 'res.partner'

    credit_limit = fields.Float(string='Credit Limit', copy=False, tracking=10)

    flag = fields.Boolean(default=False, compute='_compute_credit_limit')

    @api.depends('date', 'write_date')
    def _compute_credit_limit(self):
        for rec in self:
            user_flag = self.env['res.users'].has_group('customer_credit_limit.edit_credit_limit_in_partner')
            
            if user_flag == True:
                rec.flag = True
                
            else:
                rec.flag = False