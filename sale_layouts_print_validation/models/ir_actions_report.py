# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class ActionReport(models.Model):
    _inherit = 'ir.actions.report'


    def _is_sale_layout_report(self, report_ref):
        return self._get_report(report_ref).report_name in ('sale.report_saleorder_pro_forma', 'sale.report_saleorder', 'ro_receipt_layout.print_payment_template', 'ro_receipt_layout.print_simp_receipt_template')

    def _render_qweb_pdf(self, report_ref, res_ids=None, data=None):
        # Check for reports only available for invoices.
        # + append context data with the display_name_in_footer parameter
        if self._is_sale_layout_report(report_ref):
            orders = self.env['sale.order'].browse(res_ids)
            if any(x.state not in ('sale','done') for x in orders):
                raise UserError(_("Only Confirmed Orders could be printed."))

        return super()._render_qweb_pdf(report_ref, res_ids=res_ids, data=data)
