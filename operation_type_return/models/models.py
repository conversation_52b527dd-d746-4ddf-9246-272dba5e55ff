# -*- coding: utf-8 -*-
from odoo import models,fields,api

# from odoo.addons.sale_mrp.models.mrp_bom import MrpBom

# def _ensure_bom_is_free(self):
#     product_ids = []
#     for bom in self:
#         if bom.type != 'phantom':
#             continue
#         product_ids += bom.product_id.ids or bom.product_tmpl_id.product_variant_ids.ids
#     if not product_ids:
#         return
#     lines = self.env['sale.order.line'].sudo().search([
#         ('state', 'in', ('sale', 'done')),
#         ('invoice_status', 'in', ('no', 'to invoice')),
#         ('product_id', 'in', product_ids),
#         ('move_ids.state', '!=', 'cancel'),
#     ])
#     # if lines:
#     #     product_names = ', '.join(lines.product_id.mapped('display_name'))
#     #     raise UserError(_('As long as there are some sale order lines that must be delivered/invoiced and are '
#     #                         'related to these bills of materials, you can not remove them.\n'
#     #                         'The error concerns these products: %s', product_names))

# MrpBom._ensure_bom_is_free = _ensure_bom_is_free


class StockPickingType(models.Model):
    _inherit = 'stock.picking.type'

    is_return = fields.Boolean()
