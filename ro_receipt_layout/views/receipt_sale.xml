<odoo>
    <data>

        <record id="paperformat_portrait" model="report.paperformat">
            <field name="name">Sale Receipt</field>
            <field name="default" eval="True"/>
            <field name="format">custom</field>
            <field name="page_height">105</field>
            <field name="page_width">70</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">3</field>
            <field name="margin_bottom">3</field>
            <field name="margin_left">5</field>
            <field name="margin_right">5</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">0</field>
            <field name="dpi">95</field>
        </record>
  
        <record id="action_report_payment_view_details" model="ir.actions.report">
            <field name="name">Sale Direct Receipt</field>
            <field name="model">sale.order</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_receipt_layout.print_payment_template</field>
            <field name="report_file">ro_receipt_layout.print_payment_template</field>
            <field name="paperformat_id" ref="ro_receipt_layout.paperformat_portrait"/>
            <field name="binding_model_id" ref="sale.model_sale_order"/>
            <field name="binding_type">report</field>
        </record>
        <!-- <report
            id="report_payment_view_details"
            string="Sale Receipt"
            model="sale.order"
            report_type="qweb-pdf"
            name="ro_receipt_layout.print_payment_template"
            file="ro_receipt_layout.print_payment_template"
            paperformat="paperformat_portrait"
            menu="True"/> -->

        <template id="custom_external_layout_standard">
            <t t-if="not company">
                <!-- Multicompany -->
                <t t-if="company_id">
                    <t t-set="company" t-value="company_id"/>
                </t>
                <t t-elif="o and 'company_id' in o">
                    <t t-set="company" t-value="o.company_id.sudo()"/>
                </t>
                <t t-else="else">
                    <t t-set="company" t-value="res_company"/>
                </t>
            </t>

            <div class="article o_report_layout_standard">
                <t t-raw="0"/>
            </div>

            
        </template>
  
        <!--report-->
        <template id="print_payment_template">
            <t t-call="web.html_container">
                <t t-call="ro_receipt_layout.custom_external_layout_standard">
                    <div class="page" style="font-size:20pt !important; direction: ltr;">
                        <t t-foreach="docs" t-as="doc">              
                            <div class="col-md-6" style="padding:0">
                                <div style="margin:0 auto;width:100%;text-align:center;font-size:14px;">
                                    <img t-if="doc.company_id.logo" class=" text-center" t-att-src="image_data_uri(doc.company_id.logo)" style="max-height: 100px;max-width: 100px;text-center" alt="Logo"/>
                                    <!--<div t-field="doc.company_id.name"/>-->
                                    <!-- <div t-field="doc.company_id.phone"/> -->
                                    <!--<span>*********</span>-->
                                    <!-- <div t-field="doc.team_id.name"/> -->
                                </div>
                                <div style="height:1px;background:black;"/>
                                <div style="font-size:14px;font-weight:bold;text-decoration:underline;margin:4px auto;width:100%;text-align:center;">
                                    فاتورة
                                </div>
                                <table style="width:100%;font-size:14px;">
                                    <tbody>
                                        <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-field="doc.name"/>
                                                <span style="padding-right:.6rem;">:</span><label style="margin-bottom:0">مسلسل<span> </span>صرف</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-esc="context_timestamp(doc.date_order).strftime('%H:%M:%S')"/>
                                                <span style="padding-right:.6rem;">:</span><label style="margin-bottom:0">وقت<span> </span>الصرف</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-esc="context_timestamp(doc.date_order).strftime('%m/%d/%Y')"/>
                                                <span style="padding-right:.6rem;">:</span><label style="margin-bottom:0">تاريخ<span> </span>الصرف</label>
                                            </td>
                                        </tr>
                                        <!-- <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-field="doc.partner_id.name"/>
                                                <span>:</span><label style="margin-bottom:0">العميل</label>
                                            </td>
                                        </tr> -->
                                    </tbody>
                                </table>
                                <div style="height:1px;background:black;"/>
                                <div style="margin-bottom:4px;font-size:14px;font-weight:bold;text-decoration:underline;text-align:end;float:right;clear:both;width:10rem;font-size:10px">
                                    <span>:بيانات العميل</span>
                                </div>
                                <table style="width:100%;font-size:14px;">
                                    <tbody>
                                        <tr>
                                            <td style="text-align: right;" colspan="3">
                                                <!-- <t t-if="doc.partner_id.parent_id">
                                                    <span style="padding:2px;" t-field="doc.partner_id.parent_id.name"/>
                                                    <span>,</span>
                                                </t> -->
                                                <span style="padding:2px;" t-field="doc.partner_id.display_name"/></td><td style="text-align: right;">
                                                <label style="margin-bottom:0 ;margin-right:0px">الاسم</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right; " colspan="2">
                                            
                                                <span style="padding:2px;" t-field="doc.partner_id.street"/></td><td style="text-align: right;">
                                                <span style="padding:2px;" t-field="doc.partner_id.street2"/></td><td style="text-align: right;">
                                                <label style="margin-bottom:0 ;margin-right:0px">العنوان</label>
                                                
                                            </td>
                                        </tr>
                                        <!-- <tr>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;" t-field="doc.partner_id.mobile"/>
                                                <span>:</span><label style="margin-bottom:0">تليفون<span> </span>آخر</label>
                                            </td>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;" t-field="doc.partner_id.phone"/>
                                                <span>:</span><label style="margin-bottom:0">التليفون</label>
                                            </td>
                                        </tr> -->

                                        <tr>
                                            <td style="text-align: right;">
                                            </td>
                                            <td style="text-align: right;">
                                            </td>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;" t-field="doc.partner_id.mobile"/>
                                            </td>
                                            <td style="text-align: right;">
                                                <label style="margin-bottom:0;margin-right:0px">الهاتف المحمول</label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table style="border-top:1px solid black;font-size:14px;width:100%;margin-top:4px;">
                                    <thead>
                                        <th style="border:1px solid black; text-align:right;padding-right:6px;">ج</th>
                                        <th style="border:1px solid black; text-align:center;padding-right:6px;">س</th>
                                        <th style="border:1px solid black; text-align:center;padding-right:8px;">ك</th>
                                        <th style="border:1px solid black; text-align:center;padding:8px;">الصنف</th>
                                    </thead>
                                    <tbody style="border-top:1px solid black;">
                                        <t t-set="qty" t-value="0"/>
                                        <t t-foreach="doc.order_line" t-as="line">
                                            <!-- <tr>
                                                <td style="padding-right:8px;text-align:center;">
                                                    <span t-esc="'{:,.2f}'.format(line.product_id.general_price)"/>
                                                </td>
                                                <td style="padding-right:8px;text-align:center;">
                                                    <span t-esc="'{:,.2f}'.format(line.price_subtotal)"/>
                                                </td>
                                                <td style="padding-right:8px;text-align:center;">
                                                    <span t-esc="'{:.2f}'.format(line.price_unit)"/>
                                                </td>
                                                <td style="padding-right:8px;text-align:center;">
                                                    <span t-esc="'{:.2f}'.format(line.product_uom_qty)"/>
                                                </td>
                                                <td>
                                                    <span style="word-break:break-all;text-align:left;" t-field="line.product_id.display_name"/>
                                                </td>
                                            </tr> -->


                                            <tr>
                                                <td style="border:1px solid black;padding-right:8px;text-align:center;vertical-align: middle;">
                                                    <center>
                                                        <span t-esc="'{:,.2f}'.format(line.price_subtotal)"/>
                                                    </center>
                                                </td>
                                                <td style="border:1px solid black;padding-right:8px;text-align:center;vertical-align: middle;">
                                                    <center>
                                                        <span t-esc="'{:,.2f}'.format(line.price_unit)"/>
                                                    </center>
                                                </td>
                                                <td style="border:1px solid black;padding-right:8px;text-align:center;vertical-align: middle;">
                                                    <center>
                                                        <span t-esc="int(line.product_uom_qty)"/>
                                                        <!-- <span t-esc="'{:,.2f}'.format(line.quantity)"/> -->
                                                    </center>
                                                </td>
                                                <td name="td_name">
                                                    <span t-field="line.product_id.display_name"/>

                                                    <!-- <t t-set="product_name" t-value="line.product_id.name.split('-')"/>
                                                    <t t-set="product_variant" t-value="line.product_id.display_name.split('(')"/>
                                                    <t t-set="ar_name" t-value=""/>
                                                    <t t-if="len(product_name)&gt;2">
                                                        <t t-set="ar_name" t-value="product_name[0]"/>
                                                        <t t-set="en_name" t-value="product_name[1]+product_name[2]"/>
                                                    </t>
                                                    <t t-elif="len(product_name)&gt;1">
                                                        <t t-set="ar_name" t-value="product_name[0]"/>
                                                        <t t-set="en_name" t-value="product_name[1]"/>
                                                    </t>
                                                    <t t-else="">
                                                        <t t-set="ar_name" t-value="product_name[0]"/>
                                                        <t t-set="en_name" t-value="product_name[0]"/>
                                                    </t>
                                                    
                                                    <t t-if="len(product_variant)&gt;1" t-set="variant" t-value="product_variant[1]"/>
                                                    <p style="word-break:break-all;text-align:right;">
                                                        <t t-if="doc.partner_id.lang == 'ar_001'">
                                                            <span t-esc="ar_name"/>
                                                        </t>
                                                        <t t-else="">
                                                            <span t-esc="en_name"/>
                                                        </t>
                                                        <br/>
                                                        <span t-if="variant" t-esc="variant[:-1]"/>
                                                    </p> -->
                                                </td>
                                            </tr>
                                            <t t-set="qty" t-value="qty+line.product_uom_qty"/>
                                        </t>
                                    </tbody>
                                </table>
                                <div style="height:1px;background:black;"/>
                                <table style="width:100%;font-size:14px;">
                                    <tbody>
                                        <t t-set="remaining_dept" t-value="doc.partner_id.total_due + doc.amount_total"/>
                                        <tr>
                                            <td style="text-align: right; ">
                                                <span style="padding:2px;" t-esc="len(doc.order_line)"/>
                                                <span>:</span><label style="margin-bottom:0">عدد<span> </span>الاصناف</label>
                                            </td>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;" t-esc="'{:.2f}'.format(doc.total_price)"/>
                                                <span>:</span><label style="margin-bottom:0">الإجمالى</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right; ">
                                                <span style="padding:2px;" t-esc="qty"/>
                                                <span>:</span><label style="margin-bottom:0">الكميه</label>
                                            </td>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;" t-esc="'{:.2f}'.format(doc.py_total_discount)"/>
                                                <span>:</span><label style="margin-bottom:0">الخصم</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right; "/>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;" t-esc="'{:.2f}'.format(doc.amount_tax)"/>
                                                <span>:</span><label style="margin-bottom:0;width: 50px;">الضريبه</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-esc="doc.amount_total"/>
                                                <span>:</span><label style="margin-bottom:0">الصافى</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-esc="doc.amount_paid"/>
                                                <span>:</span><label style="margin-bottom:0">المدفوع</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-esc="doc.amount_due"/>
                                                <span>:</span><label style="margin-bottom:0">المتبقي</label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;" t-esc="remaining_dept"/>
                                                <span>:</span><label style="margin-bottom:0">باقي المديونية</label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div style="height:1px;background:black;"/>
                                
                                <div style="font-size:14px;border-top:1px solid black;">
                                    <center>Printed by :
                                        <span t-esc="user.name"/>
                                    </center>
                                    <center>
                                        <!-- 01015431795 - ********* -->
                                        <!-- 01025920008 -->
                                    </center>
                                    <!--<div>Printed on :-->
                                    <!--    <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M:%S')"/>-->
                                    <!--</div>-->
                                    <div>
                                        <center>Powered by www.roayatec.com</center>
                                    </div>
                                    <center>Tax ID :
                                        <span t-esc="doc.company_id.vat"/>
                                    </center>
                                </div>
                            </div>
                            <t t-esc="doc.change_size_page(doc.order_line)"/>
                        </t>
                    </div>
                </t>
            </t>
        </template>

        


        <!-- simpl receipt  -->
        <record id="paperformat_simp_receipt" model="report.paperformat">
            <field name="name">Sale Number</field>
            <field name="default" eval="True"/>
            <field name="format">A7</field>
            <field name="page_height">0</field>
            <field name="page_width">0</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">3</field>
            <field name="margin_bottom">3</field>
            <field name="margin_left">5</field>
            <field name="margin_right">5</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">30</field>
            <field name="dpi">88</field>
        </record>

        <record id="action_report_payment_view_simpl" model="ir.actions.report">
            <field name="name">Simple Direct Receipt</field>
            <field name="model">sale.order</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_receipt_layout.print_simp_receipt_template</field>
            <field name="report_file">ro_receipt_layout.print_simp_receipt_template</field>
            <field name="paperformat_id" ref="ro_receipt_layout.paperformat_simp_receipt"/>
            <field name="binding_model_id" ref="sale.model_sale_order"/>
            <field name="binding_type">report</field>
        </record>
        <!-- <report
            id="report_payment_view_simpl"
            string="Simple Receipt"
            model="sale.order"
            report_type="qweb-pdf"
            name="ro_receipt_layout.print_simp_receipt_template"
            file="ro_receipt_layout.print_simp_receipt_template"
            paperformat="paperformat_simp_receipt"
            menu="True"/> -->
        <!--report-->
        <template id="print_simp_receipt_template">
            <t t-call="web.html_container">
                <t t-call="ro_receipt_layout.custom_external_layout_standard">
                    <div class="page" style="font_size:16pt !important;">
                        <t t-foreach="docs" t-as="doc">
                            <div class="col-md-6">
                                <br/>
                                <center>

                                    <img t-if="doc.company_id.logo" class=" text-center"
                                        t-att-src="image_data_uri(doc.company_id.logo)"
                                        style="max-height: 100px;max-width: 150;text-center" alt="Logo"/>
                                    <br/>
                                    <span t-field="doc.company_id.name"/><br/>
                                    Tel:<span t-field="doc.company_id.phone"/>  -  <span t-field="doc.company_id.email"/><br/>
                                    <span t-field="doc.company_id.website"/><br/>
                                    ------------------------------------------<br/>
                                    <h4>Customer: <span t-field="doc.partner_id.name"/><br/></h4>
                                    <h4><span t-field="doc.name"/><br/></h4>
                                    <h4>Total: <span t-field="doc.amount_total"/><br/></h4>
                                
                                    
                                    Served by <span t-field="doc.user_id.name"/>
                                </center>
                                   
                            </div>
                        </t>
                    </div>
                </t>
            </t>
        </template>


        <record id="sale_order_view_receipt_report" model="ir.ui.view">
            <field name="name">sale.order.view.receipt_report</field>
            <field name="model">sale.order</field>
            <field name="type">form</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button type="action" string="Receipt" class="oe_highlight"
                            name="%(ro_receipt_layout.action_report_payment_view_details)d"
                            attrs="{'invisible':[('state','not in',('sale','done'))]}"/>
                    <button type="action" string="Simple Receipt" class="oe_highlight"
                            name="%(ro_receipt_layout.action_report_payment_view_simpl)d"
                            attrs="{'invisible':[('state','not in',('sale','done'))]}"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
