from odoo import models, fields, api

class EmployeeContactInherited(models.Model):
    _inherit = 'hr.contract'

    ro_has_tax = fields.Boolean('Has Tax')

    ro_bonas = fields.Float(string='حافز')
    ro_shift = fields.Monetary(string='بدل وردية')
    ro_company_contribution = fields.Monetary(string='مساهمه الشركة')
    ro_replacement = fields.Monetary(string='بدل انتقال')
    ro_regularity = fields.Monetary(string='بدل انتظام')
    ro_quarter_value = fields.Float('قيمة الكوارتر')
    ro_work_nature = fields.Monetary(string='بدل طبيعة عمل')

    ro_tax_10 = fields.Float('الضريبة يوم 10', compute="compute_contract_values")
    ro_tax_30 = fields.Float('الضريبة يوم 30', compute="compute_contract_values")
    
    ro_insurance = fields.Float('حصة التامينات', compute="compute_contract_values")

    ro_10_day_wage = fields.Float('مرتب يوم 10', compute="compute_contract_values")
    ro_30_day_wage = fields.Float('مرتب يوم 30', compute="compute_contract_values")

    ro_net_wage = fields.Float('صافي المرتب', compute="compute_contract_values")
    ro_net_income = fields.Float('صافي الدخل', compute="compute_contract_values",digits=(16, 2))
    
    
    ro_old_net_income = fields.Float('صافي الدخل القديم')
    ro_new_raise = fields.Float('قيمة العلاوة الجديدة')
    ro_total_increase_percentage = fields.Float('اجمالي نسبة الزيادة')
    ro_net_increase_percentage = fields.Float('صافي نسبة الزيادة')

    @api.depends('wage','ro_shift','ro_company_contribution','ro_replacement','ro_bonas','ro_regularity')
    def compute_contract_values(self):
        for rec in self:
            rec.ro_insurance = rec.basic_salary * .11
            if rec.ro_has_tax:
                rec.ro_tax_30 = 0
                rec.ro_tax_10 = 0
            else:
                rec.ro_tax_30 = rec.calculate_eg_tax(rec.wage + rec.ro_shift + rec.ro_company_contribution + rec.ro_replacement - rec.ro_insurance)
                rec.ro_tax_10 = rec.calculate_eg_tax(rec.wage + rec.ro_shift + rec.ro_company_contribution + rec.ro_replacement + rec.ro_regularity + rec.ro_bonas - rec.ro_insurance) - rec.ro_tax_30

            rec.ro_30_day_wage = rec.wage + rec.ro_company_contribution + rec.ro_replacement + rec.ro_shift - rec.ro_tax_30 - rec.ro_insurance
            rec.ro_10_day_wage = rec.ro_regularity + rec.ro_bonas - rec.ro_tax_10

            rec.ro_net_wage = rec.ro_30_day_wage + rec.ro_10_day_wage
            # (rec.wage + rec.ro_shift + rec.ro_company_contribution + rec.ro_replacement + rec.ro_regularity + rec.ro_bonas - rec.ro_insurance - rec.ro_tax_30 - rec.ro_tax_10)
            rec.ro_net_income = (rec.ro_net_wage + (rec.ro_quarter_value)/3)