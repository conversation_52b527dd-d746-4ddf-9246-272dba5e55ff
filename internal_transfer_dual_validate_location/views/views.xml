<odoo>
    <data>
        <record model="ir.ui.view" id="stock_picking_form_inherit">
            <field name="name">stock picking form view inherit</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="button_validate" string="Second Validate"
                    type="object" class="oe_highlight" attrs="{'invisible':[('state','!=','validate2')]}"/>
                </xpath>
                <xpath expr="//field[@name='move_ids_without_package']/tree/field[@name='quantity_done']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|',('product_id', '=', False),('parent.state', 'in', ('validate2','done','cancel'))], 'column_invisible':[('parent.state', '=', 'draft'), ('parent.immediate_transfer', '=', False)]}</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_stock_move_line_tree_done_qty" model="ir.ui.view">
            <field name="name">view.stock.picking.form.done.qty</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='qty_done']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|',('parent.state', 'in', ('validate2','done','cancel')),('state', 'in', ('done', 'cancel')), ('is_locked', '=', True)]}</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_stock_move_line_tree_done_qty_simple" model="ir.ui.view">
            <field name="name">view.stock.picking.form.done.qty.simple</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='qty_done']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', '|',('parent.state', 'in', ('validate2','done','cancel')), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True), '&amp;', ('package_level_id', '!=', False), ('parent.picking_type_entire_packs', '=', True)]}</attribute>
                </xpath>
            </field>
        </record>
        
    
        <record model="ir.ui.view" id="stock_move_line_search_validate2">
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.stock_move_line_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='product_id']" position="replace">
                    <field name="picking_id" string="Transfer"/>
                </xpath>
                <xpath expr="//field[@name='picking_id']" position="replace">
                    <field name="product_id"/>
                </xpath>
            </field>
        </record>
        <record model="ir.ui.view" id="stock_picking_search_validate2">
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_internal_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='available']" position="after">
                    <filter name="available2" string="Second Validate" domain="[('state', '=', 'validate2')]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
