# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools.misc import formatLang, format_date, get_lang

class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    partner_currency_id = fields.Many2one('res.currency', related='partner_id.currency_id', string="Currency")
    total_due = fields.Monetary(string='Total Due', compute="get_total_due")

    def get_total_due(self):
        for rec in self:
            rec.total_due = 0
            if rec.partner_id and rec.partner_id.parent_id:
                rec.total_due = rec.partner_id.parent_id.total_due
            elif rec.partner_id:
                rec.total_due = rec.partner_id.total_due

    credit_check = fields.Boolean(
        string='Credit',
        default=False,
        tracking=8
    )
    
    
    def action_confirm(self):

        if not self.credit_check and self.total_due >= 0:
            raise ValidationError(_('Customer balance not enough'))
        elif not self.credit_check and self.total_due < 0 and self.amount_total > abs(self.total_due):
            raise ValidationError(_('Customer balance not enough'))

        return super(SaleOrder, self).action_confirm()
