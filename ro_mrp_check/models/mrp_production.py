from odoo import models, fields
from odoo.exceptions import UserError

class MrpProdution(models.Model):
    
    _inherit = 'mrp.production'
    
    allow_without_consumable =fields.Boolean(default=False, string="Allow Confirm", copy=False,
                                             help="Allow confirming Manufacturing order even if has no consumable product or have them with wrong quantities.")
    
    def button_mark_done(self):
        if not self.allow_without_consumable:
            
            bom_consumables = []
            for bline in self.bom_id.bom_line_ids:
                if bline.product_id.detailed_type == 'consu':
                    bom_consumables.append(bline)
                    
            mo_consumables = []
            for line in self.move_raw_ids:
                if line.product_id.detailed_type == 'consu':
                    mo_consumables.append(line)
                
            if len(mo_consumables) != len(bom_consumables):
                raise UserError("The Manufacturing Order has Different Consumable Products than BOM")
            
            found = []
            for bom_line in bom_consumables:
                for line in self.move_raw_ids:
                    if line.product_id == bom_line.product_id:
                        found.append((line, bom_line))
                        break
                
            if len(found) < len(bom_consumables):
                raise UserError("The Manufacturing Order has Different Consumable Products than BOM")

            factor = self.product_qty/self.bom_id.product_qty
            for line, bom_line in found:
                needed_qty = bom_line.product_qty * factor
                if line.quantity_done != 0:
                    if line.quantity_done != needed_qty:
                        raise UserError(f"{line.product_id.name}'s Quantity doesn't match the needed Quantity ({needed_qty})")
                elif line.forecast_availability != needed_qty:
                    raise UserError(f"{line.product_id.name}'s Quantity doesn't match the needed Quantity ({needed_qty})")

        return super().button_mark_done()
    
    
    def action_confirm(self):
        if not self.allow_without_consumable:
            
            bom_consumables = []
            for bline in self.bom_id.bom_line_ids:
                if bline.product_id.detailed_type == 'consu':
                    bom_consumables.append(bline)
                    
            mo_consumables = []
            for line in self.move_raw_ids:
                if line.product_id.detailed_type == 'consu':
                    mo_consumables.append(line)
                
            if len(mo_consumables) != len(bom_consumables):
                raise UserError("The Manufacturing Order has Different Consumable Products than BOM")
            
            found = []
            for bom_line in bom_consumables:
                for line in self.move_raw_ids:
                    if line.product_id == bom_line.product_id:
                        found.append((line, bom_line))
                        break
                
            if len(found) < len(bom_consumables):
                raise UserError("The Manufacturing Order has Different Consumable Products than BOM")
            
        return super().action_confirm()
    