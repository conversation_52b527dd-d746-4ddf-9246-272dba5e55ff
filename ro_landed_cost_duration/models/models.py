# -*- coding: utf-8 -*-

from odoo import models, fields, api,_, SUPERUSER_ID
from odoo.exceptions import UserError

class StockLandedCostLines(models.Model):
    _inherit='stock.landed.cost.lines'

    duration = fields.Float()
    
    flag = fields.Boolean(string='flag', default=False)

    def read(self, fields=None, load='_classic_read'):
        res = super(StockLandedCostLines, self).read(fields=fields, load=load)
        for r in self:
            if r.flag == True and r.duration != 0:
                r.write({'flag':False})
                r._onchange_duration_get_cost()
        return res

    @api.onchange('account_id','duration')
    def _onchange_duration_get_cost(self):
        for rec in self:
            rec.price_unit = rec.duration * rec.account_id.landed_cost_rate