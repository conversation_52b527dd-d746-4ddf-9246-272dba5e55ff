<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>
     <record id="account_invoice_report_view_inherited" model="ir.ui.view">
            <field name="name">account.invoice.report.inherit</field>
            <field name="model">account.invoice.report</field>
            <field name="inherit_id" ref="account.account_invoice_report_view_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//tree//field[@name='move_type']" position="after">
                        <field name="partner_ref" optional="hide"/>
                        <field name="ref" optional="hide"/>
                        <field name="class_id" optional="hide"/>
                        <field name="reason" optional="hide"/>
                        <field name="commercial_partner_id" optional="hide"/>
                        <field name="discount_amt" optional="hide"/>
                        <field name="tax_amt" optional="hide"/>
                        <field name="product_uom_id" optional="hide"/>
                </xpath>
            </field>
        </record>




        <!-- Inherit the existing purchase.report search view -->
        <record id="account_invoice_report_searchview_inherited" model="ir.ui.view">
            <field name="name">account.invoice.report.searchview.inherit</field>
            <field name="model">account.invoice.report</field>
            <field name="inherit_id" ref="account.view_account_invoice_report_search"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//search/filter[@name='creditnote']" position="after">

                        <separator/>
                        <filter string="Customer Code" name="partner_ref" domain="[]" context="{'group_by': 'partner_ref'}"/>
                        
                        <separator/>
                        <filter string="Main Code" name="ref" domain="[]" context="{'group_by': 'ref'}"/>

                        <separator/>
                        <filter string="Return Reason" name="reason" domain="[]" context="{'group_by': 'reason'}"/>

                        <separator/>
                        <filter string="Main Partner" name="commercial_partner_id" domain="[]" context="{'group_by': 'commercial_partner_id'}"/>

                        <separator/>
                        <filter string="Tax Amount" name="tax_amt" domain="[]" context="{'group_by': 'tax_amt'}"/>

                        <separator/>
                        <filter string="Unit Of Measure" name="product_uom_id" domain="[]" context="{'group_by': 'product_uom_id'}"/>
                    </xpath>
            
                </data>    
            </field>
        </record>

</data>
</odoo>
