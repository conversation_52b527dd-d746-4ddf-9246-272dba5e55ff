from odoo import fields, models, api


class AccountInvoiceReport(models.Model):

    _inherit = "account.invoice.report"

    partner_ref = fields.Char(string='Customer Code', readonly=True)
    ref = fields.Char(string='Main Code', readonly=True)
    class_id = fields.Many2one(string='Class', comodel_name='partner.class', readonly=True)
    reason = fields.Char(string="Return Reason", readonly=True)
    parent_partner_id = fields.Many2one('res.partner', string='Parent Partner', readonly=True)
    # currency_id = fields.Many2one('res.currency',)
    tax_amt = fields.Monetary(string='Tax Amount', readonly=True)
    discount_amt = fields.Monetary(string='Discount Amount', readonly=True)

    def _select(self):
        # line.ro_tax_amount as ro_tax_amount,
        return super(AccountInvoiceReport, self)._select() + """,          
            move_partner.ref as partner_ref,
            partner.ref as ref,
            partner.class_id as class_id,
            move_partner.parent_id as parent_partner_id,
            CASE WHEN line.balance>0 THEN -(line.price_total - line.price_subtotal) ELSE (line.price_total - line.price_subtotal) END as tax_amt,
            move.reason as reason,
            (line.quantity * line.price_unit * line.discount/100) as discount_amt

           """
    @api.model
    def _from(self):
        return super(AccountInvoiceReport, self)._from() + """       
            LEFT JOIN res_partner move_partner ON move_partner.id = move.partner_id
        """