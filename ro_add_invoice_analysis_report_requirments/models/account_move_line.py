from odoo import api, fields, models

class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'


    ro_tax_amount = fields.Monetary(string='Tax Amount', compute='_compute_taxes', store=True)

    @api.depends('price_subtotal', 'tax_ids')
    def _compute_taxes(self):
        for line in self:
            #I  use the compute_all() method provided by Odoo's tax_ids to compute taxes based on the provided parameters.
            taxes = line.tax_ids.compute_all(line.price_subtotal, line.currency_id, line.quantity, line.product_id)
            # total_included mean that total amount that includes the tax. It's the total amount that the customer will actually pay, including the tax.
            # total_excluded mean that  total amount excluding tax. It's the base amount before any tax is applied.
            line.ro_tax_amount = taxes['total_included'] - taxes['total_excluded']

   
