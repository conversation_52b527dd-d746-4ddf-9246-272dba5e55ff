from odoo import models, fields, api
import time

class PurchaseRequisitionLine(models.Model):
    
    _inherit = 'purchase.requisition.line'
    
    available_qty = fields.Float(compute="_compute_available_qty")
    
    
    
    @api.depends('product_id')
    def _compute_available_qty(self):
        for rec in self:
            if rec.product_id:
                rec.available_qty = rec.product_id.with_context({'location':rec.requisition_id.picking_type_id.default_location_dest_id.id}).free_qty
            else:
                rec.available_qty=False