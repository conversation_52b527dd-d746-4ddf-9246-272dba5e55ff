from odoo import models, fields, api, _
from datetime import datetime


class SaleReportInherit(models.Model):
    _inherit = 'sale.report'

    ro_qty_returned = fields.Float(string="Qty Returned")


    def _select_additional_fields(self):
        res = super()._select_additional_fields()
        res['ro_qty_returned'] = "l.ro_qty_returned"
        return res
    
    def _group_by_sale(self):
        res = super()._group_by_sale()
        res += """,
            l.ro_qty_returned"""
        return res