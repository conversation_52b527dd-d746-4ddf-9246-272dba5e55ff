<?xml version="1.0" encoding="utf-8" ?>
<odoo>
  <record id="view_order_form" model="ir.ui.view">
    <field name="name">sale.order.form - sale_line_returned_qty</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form" />
    <field name="arch" type="xml">
      <xpath expr="//page/field[@name='order_line']/form/group/group/div[@name='delivered_qty']" position="after">
        <label for="ro_qty_returned" string="Returned" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}" />
        <div name="returned_qty" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}">
          <field name="ro_qty_returned" />
        </div>
      </xpath>
      <xpath expr="//page/field[@name='order_line']/tree/field[@name='qty_delivered']" position="after">
        <field name="ro_qty_returned" string="Returned" attrs="{
                        'column_invisible': [('parent.state', 'not in', ['sale', 'done'])]
                    }" optional="show" />
      </xpath>
    </field>
  </record>
</odoo>
