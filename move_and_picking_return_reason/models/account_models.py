# -*- coding: utf-8 -*-

from odoo import api,fields,models,_


class AccountMove(models.Model):
    _inherit = 'account.move'
    
    reason = fields.Char()

class AccountMoveReversal(models.TransientModel):
    _inherit = 'account.move.reversal'

    reason_id = fields.Many2one('res.reason', string='Reason')
    reason = fields.Char(string='Reason', related='reason_id.name')


    def _prepare_default_reversal(self, move):
        res = super(AccountMoveReversal, self)._prepare_default_reversal(move)

        res['reason'] = self.reason_id.name
        return res 

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _prepare_invoice(self):
        res = super(SaleOrder, self)._prepare_invoice()
        
        reason = self._context.get('reason')
        if reason:
            context_vals = {
                'reason': reason,
            }
            res.update(context_vals)
        
        return res

class SaleAdvancePaymentInv(models.TransientModel):
    _inherit = 'sale.advance.payment.inv'

    reason_id = fields.Many2one('res.reason', string='Reason')

    has_nigative_sign = fields.Boolean(compute="_compute_has_negative_sign")

    @api.depends('sale_order_ids')
    def _compute_has_negative_sign(self):
        for rec in self:
            check_list = rec.sale_order_ids.order_line.mapped(lambda x:x.qty_to_invoice < 0)
            rec.has_nigative_sign = any(check_list)
    
    def _create_invoices(self, sale_orders):
        self.ensure_one()
        if self.advance_payment_method == 'delivered':
            context = {
                'reason': self.reason_id.name,
            }
            # Use the context when creating invoices
            return sale_orders.with_context(context)._create_invoices(final=self.deduct_down_payments)
        
        else:
            self.sale_order_ids.ensure_one()
            self = self.with_company(self.company_id)
            order = self.sale_order_ids

            # Create deposit product if necessary
            if not self.product_id:
                self.product_id = self.env['product.product'].create(
                    self._prepare_down_payment_product_values()
                )
                self.env['ir.config_parameter'].sudo().set_param(
                    'sale.default_deposit_product_id', self.product_id.id)

            # Create down payment section if necessary
            if not any(line.display_type and line.is_downpayment for line in order.order_line):
                self.env['sale.order.line'].create(
                    self._prepare_down_payment_section_values(order)
                )

            down_payment_so_line = self.env['sale.order.line'].create(
                self._prepare_so_line_values(order)
            )
            

            invoice = self.env['account.move'].sudo().create(
                self._prepare_invoice_values(order, down_payment_so_line)
            ).with_user(self.env.uid)  # Unsudo the invoice after creation

            invoice.message_post_with_view(
                'mail.message_origin_link',
                values={'self': invoice, 'origin': order},
                subtype_id=self.env.ref('mail.mt_note').id)

            return invoice
