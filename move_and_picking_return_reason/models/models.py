# -*- coding: utf-8 -*-

from odoo import api,fields,models,_


class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    return_reason = fields.Char()

class StockReturnPicking(models.TransientModel):
    _inherit = 'stock.return.picking'

    reason_id = fields.Many2one('res.reason', string='Reason')


    def _create_returns(self):
        new_picking, pick_type_id = super(StockReturnPicking, self)._create_returns()
        picking = self.env['stock.picking'].browse(new_picking)
        picking.write({
            'return_reason': self.reason_id.name,
            })
        return new_picking, pick_type_id
        