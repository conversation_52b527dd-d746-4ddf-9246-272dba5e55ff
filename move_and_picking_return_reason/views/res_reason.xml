<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="res_reason_tree_view" model="ir.ui.view">
            <field name="name">Reasons tree</field>
            <field name="model">res.reason</field>
            <field name="arch" type="xml">
                <tree string="Reasons" editable="bottom">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="res_reason_form_view" model="ir.ui.view">
            <field name="name">Reasons form</field>
            <field name="model">res.reason</field>
            <field name="arch" type="xml">
                <form string="Reasons" >
                    <sheet>
                        <group>
                            <group>
                                <field name="name" />
                            </group>
                            <group>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>


        <!-- actions opening model views on models -->
        <record id="res_reason_config_action_window" model="ir.actions.act_window">
            <field name="name">Reasons</field>
            <field name="res_model">res.reason</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree</field>
        </record>    
        <menuitem action="res_reason_config_action_window" id="res_reason_config" sequence="65" parent="stock.menu_stock_config_settings"/>
    </data>
</odoo>
