<odoo>
  <data>
    <record id="view_picking_form_return_reason" model="ir.ui.view">
      <field name="model">stock.picking</field>
      <field name="inherit_id" ref="stock.view_picking_form"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='origin']" position="before">
            <field name="return_reason" readonly="1" force_save="1" attrs="{'invisible':[('return_reason','=',False)]}"/>  
          </xpath>   
      </field>
    </record>

    <record id="stock_return_picking_form_view_inherit_add_reason" model="ir.ui.view">
      <field name="name">stock.return.picking.view.form.inherit.reason</field>
      <field name="model">stock.return.picking</field>
      <field name="inherit_id" ref="stock.view_stock_return_picking_form"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='location_id']" position="after">
          <field name="reason_id" required="1" options="{'no_create': True, 'no_create_edit':True}"/>
        </xpath>
      </field>
    </record>

  </data>
</odoo>