<odoo>
  <data>
    <record id="view_account_move_form_return_reason" model="ir.ui.view">
      <field name="model">account.move</field>
      <field name="inherit_id" ref="account.view_move_form"/>
      <field name="arch" type="xml">
          <xpath expr="//group[@name='sale_info_group']/field[@name='ref']" position="after">
            <field name="reason" readonly="1" force_save="1" attrs="{'invisible':[('reason','=',False)]}"/>  
          </xpath>   
      </field>
    </record>

    <record id="account_move_reversal_form_view_inherit_add_reason" model="ir.ui.view">
      <field name="name">account.move.reversal.view.form.inherit.reason</field>
      <field name="model">account.move.reversal</field>
      <field name="inherit_id" ref="account.view_account_move_reversal"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='reason']" position="after">
          <field name="reason_id" options="{'no_create': True, 'no_create_edit':True}" attrs="{'required':[('move_type','!=','entry')],'invisible': [('move_type', '=', 'entry')]}"/>
        </xpath>
        <xpath expr="//field[@name='reason']" position="attributes">
          <attribute name="invisible">1</attribute>
        </xpath>

      </field>
    </record>

  </data>
</odoo>