from odoo import models, fields, api

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    is_returned_quotation = fields.Boolean(
        string='Returned Quotation', 
        default=False, 
        store=True,
        compute='_compute_is_returned_quotation'
    )

    @api.depends('order_line', 'order_line.qty_delivered', 'order_line.move_ids.state')
    def _compute_is_returned_quotation(self):
        for order in self:
            is_returned = False
            if len(order.order_line)==0:
                is_returned = False
            for line in order.order_line:
                incoming_moves_done = any(
                    move.picking_id.picking_type_id.code == 'incoming' and move.state == 'done'
                    for move in line.move_ids
                )
                
                if incoming_moves_done and line.order_id.state!='draft':
                    is_returned = True
                    break

            order.is_returned_quotation = is_returned
