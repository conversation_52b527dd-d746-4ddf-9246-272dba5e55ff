<odoo>
    <data>
        <record id="account_reports.aged_receivable_report" model="account.report">
            <field name="name">Aged Receivable</field>
            <field name="filter_date_range" eval="False"/>
            <field name="filter_unfold_all" eval="True"/>
            <field name="filter_partner" eval="True"/>
            <field name="filter_period_comparison" eval="False"/>
            <field name="filter_hierarchy">never</field>
            <field name="filter_show_draft" eval="False"/>
            <field name="filter_multi_company">selector</field>
            <field name="default_opening_date_filter">today</field>
            <field name="custom_handler_model_id" ref="account_reports.model_account_aged_receivable_report_handler"/>
            <field name="line_template">account_reports.aged_report_line_template</field>
            <field name="main_template">account_reports.aged_report_main_template</field>

            <field name="column_ids">
                <record id="aged_receivable_report_invoice_date" model="account.report.column">
                    <field name="name">Invoice Date</field>
                    <field name="expression_label">invoice_date</field>
                      <field name="figure_type">date</field>
                      <field name="sortable" eval="True"/>
                </record>

                <record id="aged_receivable_report_partner_ref" model="account.report.column">
                    <field name="name">Partner Ref</field>
                    <field name="expression_label">partner_ref</field>
                      <field name="figure_type">none</field>
                      <field name="sortable" eval="True"/>
                </record>

                <record id="aged_receivable_report_partner_limit" model="account.report.column">
                    <field name="name">Credit Limit</field>
                    <field name="expression_label">partner_limit</field>
                    <field name="figure_type">none</field>
                    <field name="sortable" eval="True"/>
                </record>
            </field>

            <field name="line_ids">
                <record id="account_reports.aged_receivable_line" model="account.report.line">
                    <field name="name">Aged Receivable</field>
                    <field name="groupby">partner_id, id</field>
                    <field name="expression_ids">
                        <record id="aged_receivable_line_invoice_date" model="account.report.expression">
                            <field name="label">invoice_date</field>
                            <field name="engine">custom</field>
                            <field name="formula">_report_custom_engine_aged_receivable</field>
                            <field name="subformula">invoice_date</field>
                            <field name="auditable" eval="False"/>
                        </record>
                        
                        <record id="aged_receivable_line_partner_ref" model="account.report.expression">
                            <field name="label">partner_ref</field>
                            <field name="engine">custom</field>
                            <field name="formula">_report_custom_engine_aged_receivable</field>
                            <field name="subformula">partner_ref</field>
                            <field name="auditable" eval="False"/>
                        </record>
                        
                        <record id="aged_receivable_line_partner_limit" model="account.report.expression">
                            <field name="label">partner_limit</field>
                            <field name="engine">custom</field>
                            <field name="formula">_report_custom_engine_aged_receivable</field>
                            <field name="subformula">partner_limit</field>
                            <field name="auditable" eval="False"/>
                            <field name="figure_type">monetary</field>
                        </record>
                    </field>
                </record>
            </field>
        </record>
    </data>
</odoo>