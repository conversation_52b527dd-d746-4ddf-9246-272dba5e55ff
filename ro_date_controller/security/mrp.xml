<odoo>
    <data>
        <record id="mrp_date_control_category" model="ir.module.category">
            <field name="name">Control Mrp Date</field>
            <field name="description">group for people adjusting date planned</field>
            <field name="sequence">10</field>
        </record> 

        <record id="mrp_date_control_group" model="res.groups">
            <field name="name">Control Mrp Date</field>
            <field name="category_id" ref="mrp_date_control_category"/>
        </record>
    </data>
</odoo>