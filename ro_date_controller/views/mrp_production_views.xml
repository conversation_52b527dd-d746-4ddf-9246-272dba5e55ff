<odoo>
    <data>
        <record id="mrp_production_views_inherit" model="ir.ui.view">
            <field name="name">mrp.production.views.inherit</field>
            <field name="model">mrp.production</field>
            <field name="inherit_id" ref="mrp.mrp_production_form_view" />
            <field name="arch" type="xml" >
                <!-- <xpath expr="//group[@name='group_extra_info']/div[@class='o_row']/field[@name='date_planned_start']" position="attributes" >
                    <attribute name="groups">"ro_date_controller.mrp_date_control_group"</attribute>
                </xpath> -->
                <xpath expr="//group[@name='group_extra_info']/div[@class='o_row']/field[@name='date_planned_start']" position="replace">
                    <!-- <field name="date_planned_start"
                        attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"
                        decoration-warning="state not in ('done', 'cancel') and date_planned_start &lt; now"
                        decoration-danger="state not in ('done', 'cancel') and date_planned_start &lt; current_date"
                        decoration-bf="state not in ('done', 'cancel') and (date_planned_start &lt; current_date or date_planned_start &lt; now)"
                        groups="ro_date_controller.mrp_date_control_group"
                    /> -->
                    <field name="date_planned_start"
                        readonly="1"
                        attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"
                        decoration-warning="state not in ('done', 'cancel') and date_planned_start &lt; now"
                        decoration-danger="state not in ('done', 'cancel') and date_planned_start &lt; current_date"
                        decoration-bf="state not in ('done', 'cancel') and (date_planned_start &lt; current_date or date_planned_start &lt; now)"
                    />
                </xpath>
                <xpath expr="//group[@name='group_extra_info']/div[@class='o_row']/field[@name='date_planned_start']" position="after" >
                    <button name="action_decrement_date_start"
                        type="object"
                        string=""
                        class="btn btn-primary fa fa-minus-square-o"
                        groups="ro_date_controller.mrp_date_control_group"
                        attrs="{'invisible': ['|', ('state', 'in', ['done', 'cancel']), ('ro_decremented_once', '=', True)]}"
                    />
                </xpath>
                <xpath expr="//field[@name='company_id']" position="after" >
                    <field name="ro_decremented_once" attrs="{'invisible': 1}"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>