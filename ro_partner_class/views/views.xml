<odoo>
  <data>
    
    <!-- brand -->
    
    <!-- actions opening views on models -->
    <record id="partner_class_action_window" model="ir.actions.act_window">
      <field name="name">Partner Class</field>
      <field name="res_model">partner.class</field>
      <field name="type">ir.actions.act_window</field>
      <field name="view_mode">tree,form</field>
    </record>    
    <menuitem action="partner_class_action_window" id="partner_class" sequence="2" parent="contacts.res_partner_menu_config"/>
  
    
    

    <!-- inhert product form view  -->
    <record id="res_partner_class_inherit" model="ir.ui.view">
      <field name="name">res.partner.class.inheritd</field>
      <field name="model">res.partner</field>
      <field name="inherit_id" ref="base.view_partner_form"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='category_id']" position="after">    
            <field name="class_id" />              
          </xpath>
      </field>
    </record>

  </data>
</odoo>