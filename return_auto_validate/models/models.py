# -*- coding: utf-8 -*-

from odoo import api,fields,models,_
from odoo.addons.stock.wizard.stock_picking_return import ReturnPicking

# class StockReturnPicking(models.TransientModel):
#     _inherit = 'stock.return.picking'

def create_returns(self):
    for wizard in self:
        new_picking_id, pick_type_id = wizard._create_returns()

        picking = self.env['stock.picking'].browse(new_picking_id)
        picking.action_set_quantities_to_reservation()
        picking.button_validate()
        
    # Override the context to disable all the potential filters that could have been set previously
    ctx = dict(self.env.context)
    ctx.update({
        'default_partner_id': self.picking_id.partner_id.id,
        'search_default_picking_type_id': pick_type_id,
        'search_default_draft': False,
        'search_default_assigned': False,
        'search_default_confirmed': False,
        'search_default_ready': False,
        'search_default_planning_issues': False,
        'search_default_available': False,
    })
    return {
        'name': _('Returned Picking'),
        'view_mode': 'form,tree,calendar',
        'res_model': 'stock.picking',
        'res_id': new_picking_id,
        'type': 'ir.actions.act_window',
        'context': ctx,
    }
ReturnPicking.create_returns = create_returns