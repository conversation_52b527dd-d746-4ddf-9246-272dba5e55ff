<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="view_hr_payslip_form_inherit_add_fields" model="ir.ui.view">
            <field name="name">hr.payslip.view.form.inherit</field>
            <field name="model">hr.payslip</field>
            <field name="inherit_id" ref="hr_payroll.view_hr_payslip_form"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet/group" position="after">
                   <group>
                        <group name="Allowances">
                            <field name="ro_bonas" string="حافز" />
    
                            <field name="ro_additional_transfer" string="انتقالات الاضافى" />
                            <field name="ro_number_of_nights" string="عدد السهرات" required="1"/>
                            
                            <field name="total_value_of_nights" string="قيمة السهرات" />
                            
                            <field name="ro_quarter_value" string="قيمة الكوارتر" />
                            <field name="ro_shift" string="بدل ورديه"/>
                            <field name="ro_company_contribution" string="مساهمة الشركة"/>
                            <field name="ro_replacement" string="بدل انتقال"/>
                            <field name="ro_regularity" string="بدل انتظام"/>
                            <field name="ro_target_layers" />
                            <field name="ro_commission" />
                            <field name="ro_reward_perc" />
                            <field name="ro_kpi" />
                            <field name="ro_overtime_hours" />
                            <field name="ro_holyday_allow" />
                            <field name="ro_other_allow" />
                            <field name="ro_ramadan_perc" />
                            <field name="ro_stop_perc" />
                        </group>
                        <group name="Deduction">
                        
                            <field name="number_of_days" string="عدد الايام"/>
                            <field name="days_before_appointment" string="قيمة ايام قبل التعيين"/>
                            <field name="number_of_penalty_days" string="عدد ايام الجزاء"/>
                            <field name="discount_value_per_day" string="قيمه خصم باليوم"/>
                            <field name="ro_rent" string="نسبة خصم تالف"/>
                            <field name="ro_discount_reg" string="خصم بدل انتظام"/>
                            <field name="ro_discount_premium" string="خصم بيرميوم"/>
                            <field name="ro_discount_returns" string="خصم مرتجعات"/>
                            <field name="ro_deficit_deduction" string="خصم عجز نقديه"/>
                            <field name="ro_direct_discount" string="خصم  مباشر بالقيمه"/>

                            <field name="ro_absence" />
                            <field name="ro_absence_punsh" />
                            <field name="ro_unpaid_leave" />
                            <field name="ro_sick" />
                            <field name="ro_late" />
                            <field name="ro_previuos_tax" />
                            
                        </group>
                   </group>
                </xpath>

                <xpath expr="//page[@name='account_info']" position="inside">
                    <group>
                         <group name="Allowances">
                             <field name="overtime_allow" />
                             <field name="holyday_allow_amount" />
                             <field name="commission_allow" />
                             <field name="net_quarter_allow" />
                             <field name="net_salary" />

                         </group>
                         <group name="Deduction">
                            <field name="damadge_ded" />
                            <field name="absence_ded" />
                            <field name="sick_ded" />
                            <field name="late_ded" />
                            <field name="unpaid_ded" />
                            <field name="insurance_ded" />
                            <field name="box_5"/>
                         </group>
                    </group>
                 </xpath>
                
            </field>
        </record>


   

        <!-- Allowances -->
<!--        
        <record id="ro_hr_bonas_rule_allowance" model="hr.salary.rule">
            <field name="code">BONUS</field>
            <field name="name">حافز</field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.ro_bonas</field>
            <field name="sequence" eval="31" />
            <field name="note">Allowances</field>
        </record>


        <record id="ro_hr_transfer_rule_allowance" model="hr.salary.rule">
            <field name="code">REPLACEMENT</field>
            <field name="name">انتقالات الاضافى</field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.ro_additional_transfer</field>
            <field name="sequence" eval="31" />
            <field name="note">Allowances</field>
        </record>


        <record id="ro_hr_transfer_rule_allowance" model="hr.salary.rule">
            <field name="code">QUARTER</field>
            <field name="name">قيمةالكوارتر</field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.ro_quarter_value</field>
            <field name="sequence" eval="31" />
            <field name="note">Allowances</field>
        </record>

        <record id="ro_total_value_night_allowance" model="hr.salary.rule">
            <field name="code">NIGHT</field>
            <field name="name">قيه السهرات</field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.total_value_of_nights</field>
            <field name="sequence" eval="31" />
            <field name="note">Allowances</field>
        </record>
        -->
       

        <!-- Deduction -->

        <!-- <record id="ro_hr_before_appointment" model="hr.salary.rule">
            <field name="code">BEFORE</field>
            <field name="name">قيمة ايام قبل التعيين</field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.days_before_appointment</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>
      
       <record id="ro_hr_discount_rule" model="hr.salary.rule">
            <field name="code">DED</field>
            <field name="name">قيمه خصم باليوم</field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.discount_value_per_day</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>
      
        <record id="ro_hr_rent_rule" model="hr.salary.rule">
            <field name="code">LOANS</field>
            <field name="name">سلفيات </field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_rent</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>
      
       <record id="ro_hr_reg" model="hr.salary.rule">
            <field name="code">REGULARITY</field>
            <field name="name">خصم بدل انتظام </field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_discount_reg</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>

        <record id="ro_hr_premium_allowance" model="hr.salary.rule">
            <field name="code">PREMIUM</field>
            <field name="name">خصم بيريميوم </field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_discount_premium</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>         

         <record id="ro_hr_returnes_rule" model="hr.salary.rule">
            <field name="code">RETURN</field>
            <field name="name">خصم مرتجعات </field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_discount_returns</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>    

        <record id="ro_hr_deficit_rule" model="hr.salary.rule">
            <field name="code">CASH</field>
            <field name="name">خصم عجز نقديه </field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_deficit_deduction</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>    

        <record id="ro_hr_direct_discount_rule" model="hr.salary.rule">
            <field name="code">Deduction1</field>
            <field name="name">خصم  مباشر بالقيمه </field>
            <field name="category_id" ref="hr_payroll.ALW" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">none</field>
            <field name="condition_python">result = True </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_direct_discount</field>
            <field name="sequence" eval="31" />
            <field name="note">Deduction</field>
        </record>                          
       -->
        
    </data>
    
</odoo>