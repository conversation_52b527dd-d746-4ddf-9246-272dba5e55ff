# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.exceptions import UserError


class StockPicking(models.Model):
    _inherit = "stock.picking"

    
    is_op_manager = fields.Boolean(
        compute="_check_user_group",
        default=lambda self: self.env.user.has_group(
            "picking_operation_type_access.picking_op_manager_group"
        ),
    )

    def _check_user_group(self):
        self.is_op_manager = self.env.user.has_group(
            "picking_operation_type_access.picking_op_manager_group"
        )