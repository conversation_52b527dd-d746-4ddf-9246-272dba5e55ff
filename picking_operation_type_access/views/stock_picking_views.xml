<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_picking_op_form_inherited" model="ir.ui.view">
        <field name="name">stock.picking.form.inherited</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='origin']" position="after">
                <field name="is_op_manager" invisible="1" />
            </xpath>
            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="attrs">{'readonly': ['|', ('state', '!=', 'draft'), ('is_op_manager','=',False)]}</attribute>
            </xpath>
        </field>
    </record>
</odoo>