from odoo import models, fields, api
from odoo.exceptions import UserError

class StockTransfer(models.Model):
    _inherit = 'stock.picking'

    
    @api.constrains('location_id', 'location_dest_id')
    def _validate_transfer_locations(self):
        if self.picking_type_id.code == 'internal':
            if self.location_id.usage != 'internal' or self.location_dest_id.usage != 'internal':
                raise UserError("You can only transfer items between internal locations.")
