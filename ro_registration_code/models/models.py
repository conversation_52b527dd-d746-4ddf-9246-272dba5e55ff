
from odoo import models, fields, api, _, SUPERUSER_ID

class HrDepartment(models.Model):
    _inherit = 'hr.department'
    
    employee_sequence_id = fields.Many2one('ir.sequence', string='Employee Sequence', compute='_compute_employee_sequence_id')
    
    @api.depends('name','employee_sequence_id')
    def _compute_employee_sequence_id(self):
        """Compute the default sequence for each employee based on department."""
        for department in self:
            seq_exist = False
            if department.name:
                seq_exist = self.env['ir.sequence'].with_user(SUPERUSER_ID).search([('code','=', 'hr.employee.sequence.default.' + department.name)], limit=1)
            if seq_exist and not department.employee_sequence_id and department.name:
                department.employee_sequence_id = seq_exist
            
            elif not department.employee_sequence_id and department.name: 
                sequence = self.env['ir.sequence'].with_user(SUPERUSER_ID).create({
                    "name": _(
                            "Department default numbering (%s)",
                            department.name,
                        ),
                        'code': 'hr.employee.sequence.default.' + department.name,
                        'padding': 3,
                    })
                department.employee_sequence_id = sequence.id
            else:
                department.employee_sequence_id = False


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    registration_number = fields.Char()

    @api.model_create_multi
    def create(self, vals_list):
        employees = super(HrEmployee, self).create(vals_list)
        for employee in employees:
            department = employee.department_id
            if department and department.employee_sequence_id:
                ro_department_code = str(department.ro_department_code).zfill(3)
                new_code = department.employee_sequence_id.next_by_code(department.employee_sequence_id.code)
                if len(ro_department_code) == 3 and len(new_code) == 3:
                    employee.registration_number = ro_department_code + new_code
                    employee.ro_device_id = employee.registration_number
                else:
                    employee.registration_number = "Error: Invalid department or employee code."
            else:
                employee.registration_number = "Error: No department sequence defined."
        return employees


